import {select} from "@inquirer/prompts";
import inquirer from "inquirer";
import PluginScriptHelper from "../utils/PluginScriptHelper";
import {execSync} from "child_process";
import * as  path from "path";
import * as ncp from "copy-paste";



async function runPlugin() {
  const pluginDir = await select({
    message: '选择要运行的插件:',
    choices: [
      ...(await PluginScriptHelper.getLocalPluginOptions())
    ],

  });
  const pluginPath = PluginScriptHelper.getPluginPath(pluginDir);
  const devOutputPath = path.join(pluginPath, 'dist');
  ncp.copy(devOutputPath, (e) => {
    console.log('13123213123123', e)
  });
  console.log('完成路径>> ' + path.join(pluginPath, 'dist'));
  // 执行构建命令
  setTimeout(() => {
    execSync('npm run build:dev', {
      cwd: pluginPath,
      stdio: 'inherit'
    });
  });
}

runPlugin();
