import axios from "axios";
import * as fs from "fs";
import {getContentType} from "./utils";
import * as path from "path";

const FormData = require('form-data');


const apiService = process.env.API_SERVICE;
const fileService = process.env.FILE_SERVICE ;
const fileUsername = process.env.FILE_USERNAME;
const filePassword = process.env.FILE_PASSWORD;

const cloudService = axios.create({
  baseURL: apiService,
  timeout: 10 * 1000,
  headers: {
    'x-app-id': '200004'
  }
});


export interface PluginVersionRespVO {
  /**
   * 唯一 code
   */
  uniqueCode: string;
  /**
   * 版本号
   */
  version: string;
}

async function getPluginVersion(): Promise<PluginVersionRespVO[]> {
  const response = await cloudService.get('/admin/plugin/getAllPluginVersion').then(res => res.data);
  return response.data;
}


/**
 * 获取用户 token
 * @param host
 * @param username 用户名
 * @param password 密码
 * @returns Promise<{token: string}>
 */
async function getFileToken(): Promise<string> {
  const response = await fetch(fileService + '/api/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      username: fileUsername,
      password: filePassword
    })
  });

  const data = await response.json();
  if (data.code !== 200) {
    throw new Error(data.message);
  }

  return data.data.token;
}

async function uploadFile(file: string, filePath: string) {
  const contentType = getContentType(path.extname(file));
  const formData = new FormData();

  // 使用 createReadStream 读取文件
  const stat = fs.statSync(file);
  const token = await getFileToken();
  const response = await axios.put(fileService +  '/api/fs/put', fs.createReadStream(file), {
    headers: {
      'Content-Type': contentType,
      'Authorization': token,
      'Content-Length': stat.size,
      'File-Path': filePath,
    },
  }).then(res => res.data);
  console.log('response', response);
}

/**
 * PluginLibrarySaveReqVO
 */
export interface PluginInfoSaveRequest {
  /**
   * 插件作者名称
   */
  pluginAuthor: string;
  /**
   * 插件 code
   */
  pluginCode: string;
  /**
   * 插件描述
   */
  pluginDesc:string;
  /**
   * 插件分组
   */
  pluginGroup: "cloudVendor" | "self" | "other";
  /**
   * 插件 logo
   */
  pluginLogo: string;
  /**
   * 插件名称
   */
  pluginName: string;
  /**
   * 插件类型
   */
  pluginType: "storage";
  /**
   * 插件版本
   */
  pluginVersion: string;
  /**
   * 上传后文件路径
   */
  filePath: string;
}

async function savePluginInfo(plugin: PluginInfoSaveRequest) {
  await cloudService.post(apiService + '/admin/plugin/savePlugin', {
    ...plugin,
  });
}

export default {
  getPluginVersion,
  uploadFile,
  savePluginInfo
}
