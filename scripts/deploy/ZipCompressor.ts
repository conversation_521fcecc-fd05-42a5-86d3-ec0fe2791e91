import * as  archiver from 'archiver';
import * as fs from 'fs';
import * as path from 'path';

interface CompressionProgress {
  entries: {
    total: number;
    processed: number;
  };
  fs: {
    totalBytes: number;
    processedBytes: number;
  };
}

interface ZipOptions {
  /** 压缩等级 (0-9) */
  level?: number;
  /** 是否记录日志 */
  verbose?: boolean;
  /** 是否包含根目录 */
  includeRoot?: boolean;
}

export class ZipCompressor {
  private createZip(
    outputPath: string,
    options: ZipOptions = {}
  ): archiver.Archiver {
    const {
      level = 9,
      verbose = false,
    } = options;

    const output = fs.createWriteStream(outputPath);
    // @ts-ignore
    const archive = archiver('zip', {
      zlib: { level }
    });


    archive.on('error', (err: Error) => {
      throw err;
    });

    if (verbose) {
      archive.on('progress', (progress: CompressionProgress) => {
        const percent = (progress.entries.processed / progress.entries.total * 100).toFixed(2);
        const processedMB = (progress.fs.processedBytes / 1024 / 1024).toFixed(2);
        console.log(`压缩进度: ${percent}%`);
        console.log(`已处理: ${progress.entries.processed}/${progress.entries.total} 个文件`);
        console.log(`已压缩: ${processedMB} MB`);
      });
    }

    archive.pipe(output);
    return archive;
  }

  /**
   * 压缩目录
   * @param sourcePath - 源目录路径
   * @param outputPath - 输出zip文件的路径
   * @param options - 压缩选项
   */
  async compressDirectory(
    sourcePath: string,
    outputPath: string,
    options: ZipOptions = {}
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const archive = this.createZip(outputPath, options);
        const { includeRoot = false } = options;

        archive.on('finish', () => {
          if (options.verbose) {
            console.log('压缩完成！');
          }
          resolve();
        });

        if (!fs.existsSync(sourcePath)) {
          throw new Error(`目录不存在: ${sourcePath}`);
        }

        const stat = fs.statSync(sourcePath);
        if (!stat.isDirectory()) {
          throw new Error(`${sourcePath} 不是一个目录`);
        }

        if (includeRoot) {
          // 包含根目录的压缩方式
          archive.directory(sourcePath, path.basename(sourcePath));
        } else {
          // 不包含根目录的压缩方式
          const files = fs.readdirSync(sourcePath);
          files.forEach(file => {
            const fullPath = path.join(sourcePath, file);
            const stat = fs.statSync(fullPath);

            if (stat.isDirectory()) {
              // 如果是目录，直接添加目录
              archive.directory(fullPath, file);
            } else {
              // 如果是文件，直接添加文件
              archive.file(fullPath, { name: file });
            }
          });
        }

        archive.finalize();
      } catch (error) {
        reject(error);
      }
    });
  }
}
