import * as fs from "fs";
import * as path from "path";
import type { StoragePlugInConfig, PlugInfo } from "@xiaou66/picture-plugin";
import api from "./api";
import { execSync } from "child_process";
import { checkbox, confirm } from '@inquirer/prompts';
import inquirer from "inquirer";
import dotenv from 'dotenv'
import PluginScriptHelper from "../utils/PluginScriptHelper";

inquirer.registerPrompt('search-checkbox', require('inquirer-search-checkbox'));

/**
 * 清空指定目录
 *
 * @param outDir 指定要清空的目录路径
 */
function cleanDir(outDir: string) {
  fs.rmSync(outDir, {
    recursive: true,
    force: true,
  });
  fs.mkdirSync(outDir);
}

function numberToThreeDots(num: number): string {
  const str = num.toString();
  if (str.length <= 3) {
    // 3位数或以下，全部用点分割
    return str.split('').join('.');
  } else {
    // 超过3位数，保留前面的数字，只对最后三位分割
    const lastThree = str.slice(-2).split('').join('.');
    const front = str.slice(0, -2);
    return front + '.' + lastThree;
  }
}





async function checkVersionPlugin(pluginDir: string) {
  const pluginVersionList = await api.getPluginVersion();
  const pluginVersionObj: Record<string, string> = pluginVersionList.reduce((acc, cur) => {
    // @ts-ignore
    acc[cur.uniqueCode] = cur.version.replace(/\./g, '');
    return acc;
  }, {});
  const pluginInfo = await PluginScriptHelper.getPluginConfig(pluginDir);

  const uniqueCode = [pluginInfo.pluginCode, pluginInfo.pluginType].join('|');
  if (!pluginVersionObj[uniqueCode]) {
    // 未发布过的插件
    return;
  }
  const version = Number(pluginVersionObj[uniqueCode]);
  const localVersion = Number(pluginInfo.pluginVersion.toString().replace(/\./g, ''));
  if(localVersion <= version) {
    const configFilePath = PluginScriptHelper.getPluginConfigPath(pluginDir);
    // 自动更新文件及本地版本号
    const ver = numberToThreeDots(version + 1);
    const s = fs.readFileSync(configFilePath, 'utf-8');
    const newConfigText = s.replace(pluginInfo.pluginVersion, ver);
    fs.writeFileSync(configFilePath, newConfigText);
    console.log(`插件${pluginInfo.pluginName}版本调整为: ${ver}`);
  }
}

async function buildPlugin(pluginDir: string) {
  try {
    const pluginPath = PluginScriptHelper.getPluginPath(pluginDir);
    // 执行构建命令
    execSync('npm run build', {
      cwd: pluginPath,
      stdio: 'inherit'
    });

    const pluginInfo = await PluginScriptHelper.getPluginConfig(pluginDir);
    const zipFilePath = path.join(PluginScriptHelper.getOutputPath(),
      `${pluginInfo.pluginCode}-${pluginInfo.pluginVersion}.zip`);

    console.log(`开始上传文件: ${zipFilePath}`);
    execSync(`zip -9 -r  ${zipFilePath} ./*`, {
      cwd: path.join(pluginPath, 'dist')
    });
    const filePrefix = process.env.FILE_PREFIX || '/u-prod';
    const uploadPluginPath = `${filePrefix}/image-plugin/${pluginInfo.pluginType}/${pluginInfo.pluginCode}`;
    const remoteFilePath = path.join(uploadPluginPath, `${pluginInfo.pluginCode}-${pluginInfo.pluginVersion}.zip`);
    await api.uploadFile(
      zipFilePath,
      remoteFilePath,
    );

    // 上传 logo
    const logoPath = path.join(pluginPath, 'dist', pluginInfo.pluginLogo);
    const remoteLogoPath = path.join(uploadPluginPath, `logo${path.extname(pluginInfo.pluginLogo)}`);
    console.log(remoteLogoPath)
    await api.uploadFile(
      logoPath,
      remoteLogoPath,
    );
    console.log('开始修改插件信息');
    await api.savePluginInfo({
      ...pluginInfo,
      filePath: remoteFilePath,
      pluginLogo: remoteLogoPath,
    });
  } catch (error) {
    console.error(`构建插件失败: ${pluginDir}`, error);
    throw error;
  }
}



async function buildPlugins(pluginDirList: string[]) {
  // 清理输出目录
  cleanDir(PluginScriptHelper.getOutputPath());
  for (const pluginDir of pluginDirList) {
    await checkVersionPlugin(pluginDir);
    await buildPlugin(pluginDir);
  }
}


async function deploy() {
  const buildPluginDirList = await checkbox({
    message: '选择要更新的插件:',
    choices: [
      ...(await PluginScriptHelper.getLocalPluginOptions())
    ],
  });
  // 编写一个确认提示
  const answer = await confirm({ message: `确认要构建并发布这些插件到「${process.env.ENV_NAME}」?` });
  if (!answer) {
    return;
  }
  await buildPlugins(buildPluginDirList);
}

deploy();
