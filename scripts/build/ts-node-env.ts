// ts-node 环境变量预加载脚本
import { resolve } from 'path';
import { config } from 'dotenv';

// 默认加载根目录下的 .env 文件
config();

// 如果有命令行参数指定环境文件，则加载指定的环境文件
const envArg = process.argv.find(arg => arg.startsWith('--env='));
if (envArg) {
  const envFile = envArg.split('=')[1];
  config({ path: resolve(process.cwd(), `.${envFile}.env`) });
}
// 打印当前环境变量以供检查
console.log(process.env.ENV_NAME + '变量加载完成，当前配置:');
console.log('--------------------------');
console.log(`API服务: ${process.env.API_SERVICE || '未设置'}`);
console.log(`文件服务: ${process.env.FILE_SERVICE || '未设置'}`);
// console.log(`文件服务用户名: ${process.env.FILE_USERNAME || '未设置'}`);
// console.log(`文件服务密码: ${process.env.FILE_PASSWORD ? '已设置' : '未设置'}`);
// console.log(`文件路径前缀: ${process.env.FILE_PREFIX || '未设置'}`);
console.log('--------------------------');
