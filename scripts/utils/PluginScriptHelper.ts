import * as fs from "fs";
import * as  path from "path";
import type {PlugInfo, StoragePlugInConfig} from "@xiaou66/picture-plugin";


const workDirectory = path.resolve(__dirname, '../../');
const packagePath = path.join(workDirectory, "package");
const outDir = path.join(workDirectory, "out");


function getPluginConfigPath(pluginDir: string) {
  return path.join(packagePath, pluginDir, 'src', 'config.ts');
}

function getPluginPath(pluginDir: string) {
  return path.join(packagePath, pluginDir);
}

function getOutputPath() {
  return outDir;
}

async function getPluginConfig(pluginDir: string): Promise<PlugInfo> {
  const configFilePath = getPluginConfigPath(pluginDir);
  // 删除缓存
  delete require.cache[require.resolve(configFilePath)];
  return ((await import(configFilePath)).default as  StoragePlugInConfig).pluginInfo;
}

async function getLocalPluginOptions() {
  const pluginDirs = fs.readdirSync(packagePath);
  const options: {name: string; value: string}[] = [];
  for (const pluginDir of pluginDirs) {
    if ('.DS_Store' === pluginDir) {
      continue;
    }
    const pluginPath = path.join(packagePath, pluginDir);
    const config =await getPluginConfig(pluginDir);
    const pluginPackagePath = path.join(pluginPath, 'package.json');
    const packageFileObj = JSON.parse(fs.readFileSync(pluginPackagePath, 'utf-8'));
    if (packageFileObj.hide) {
      continue;
    }

    options.push({
      name: config.pluginName,
      value: pluginDir
    });
  }
  return options;
}



export default {
  getLocalPluginOptions,
  getPluginConfig,
  getPluginConfigPath,
  getPluginPath,
  getOutputPath
}
