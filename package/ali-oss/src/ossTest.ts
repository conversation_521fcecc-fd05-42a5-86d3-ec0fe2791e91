import path from "path";

import net from "net";
// 命名管道的路径
const pipeName = './bed_pip';

// 创建命名管道服务器
const server = net.createServer((socket: any) => {
  console.log('客户端已连接');

  // 发送消息给客户端
  socket.write('Hello from Node.js server!');

  socket.on('data', (data: any) => {
    console.log(`接收到: ${data.toString()}`);
  });

  socket.on('end', () => {
    console.log('客户端已断开连接');
  });
});

server.close();

// 监听命名管道
server.listen(pipeName, () => {
  console.log(`命名管道服务器正在监听 ${pipeName}`);
});
