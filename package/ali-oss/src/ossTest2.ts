import net from "net";
export interface IMessageResult<T=any> {
  /**
   * 执行状态
   */
  code: number;

  errorMsg?: string;

  /**
   * 行为
   */
  action: string;

  data?: T;
}

// 命名管道的路径
const pipeName = '/Users/<USER>/Library/Application Support/uTools/.pip/picture-bed-plus';
async function callServiceMethodResult<T>(pipeName: string,
                                          action: string,
                                          params: any = {}): Promise<IMessageResult<T> | undefined> {
  return new Promise((resolve, reject) => {
    try {
      // 创建客户端并连接到命名管道
      const client = net.connect(pipeName, () => {
        // 发送消息
        client.write(JSON.stringify({
          id: '1',
          action,
          pluginName: 'aaa',
          params,
        }));
      });

      // const timeout = setTimeout(() => {
      //   client.end();
      //   reject(new Error("time out"))
      //   }, 15 * 1000);
      //
      // // 监听服务器的响应
      // client.on('data', (data) => {
      //   clearTimeout(timeout);
      //   client.end();
      //   const obj = JSON.parse(data.toString()) as IMessageResult;
      //   if (obj.code === 0) {
      //     resolve(obj.data);
      //   } else {
      //     reject({
      //       code: obj.code,
      //       errorMsg: obj.errorMsg,
      //     });
      //   }
      // });
    }catch (e) {
      reject(e);
    }
  });
}
callServiceMethodResult(pipeName, 'upload.file.async', {
  filePath: '/Users/<USER>/Downloads/文件.svg',
})
  .then(data => {
    console.log(data)
  })
