import net from "net";

import path from "path";
// 命名管道的路径
const pipeName = './bed_pip';

// 创建客户端并连接到命名管道
const client = net.connect(pipeName, () => {
  console.log('已连接到命名管道服务器');

  // 发送消息
  client.write('Hello from Node.js client!');
  client.end();
});

// // 监听服务器的响应
// client.on('data', (data) => {
//   console.log(`服务器响应: ${data.toString()}`);
// });
//
// // 处理连接结束
// client.on('end', () => {
//   console.log('已断开与命名管道服务器的连接');
// });
