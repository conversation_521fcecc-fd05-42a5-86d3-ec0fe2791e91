import OSS from "ali-oss";
import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";
/**
 * 阿里云 OSS
 */
export interface IAliOssConfig {
  bucket: string;
  region: string;
  accessKeySecret: string;
  accessKeyId: string;
  filePath: string;
  domain?: string;
  style?: string;
}

export interface IAliOssExtra {
  key: string;
  originalUrl: string;
}

/**
 * 导出插件
 */
export default class AliOssStoragePlugIn extends StoragePlugIn<IAliOssConfig, IAliOssExtra> {
  private __clientMap = new Map<string, OSS>();


  protected doDestroy(): void {
    this.__clientMap.clear();
  }

  protected doInit(): void {
  }


  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IAliOssExtra>> {
    const aliOssConfig = this.readConfig(params.storageId);
    const ossClient = this.getClient(params.storageId, aliOssConfig);
    const key = this.formatUploadPath(aliOssConfig.filePath, params);
    const result = await ossClient.put(key, params.file);
    let url = result.url.replace('http', 'https');
    if (aliOssConfig.domain) {
      const re = new RegExp('^(https|http):\\/\\/(.*?)\\/');
      url = result.url.replace(re, `${aliOssConfig.domain}/`);
    }

    if (aliOssConfig.style) {
      url +=  aliOssConfig.style.includes("x-oss-process")
        ? aliOssConfig.style
        : '?x-oss-process=style/' + aliOssConfig.style;
    }

    return {
      url,
      extra: {
        originalUrl: result.url,
        key,
      }
    }
  }

  async deleteFile(params: IDeleteFileParams): Promise<boolean> {
    const ossClient = this.getClient(params.storageId);
    const extra = params.extra as IAliOssExtra;
    await ossClient.delete(extra.key);
    return true;
  }


  private getClient(storageId: string, aliOssConfig?: IAliOssConfig) {
    let ossClient = this.__clientMap.get(storageId);

    if (!ossClient) {
      if (!aliOssConfig) {
        aliOssConfig = this.readConfig(storageId);
      }
      ossClient = new OSS({
        accessKeySecret: aliOssConfig.accessKeySecret,
        accessKeyId: aliOssConfig.accessKeyId,
        region: aliOssConfig.region,
        bucket: aliOssConfig.bucket,
      });
      this.__clientMap.set(storageId, ossClient);
    }

    return ossClient;
  }


  async verifyConfig(aliOssConfig: IAliOssConfig): Promise<void> {
    const ossClient = new OSS({
      accessKeySecret: aliOssConfig.accessKeySecret,
      accessKeyId: aliOssConfig.accessKeyId,
      region: aliOssConfig.region,
      bucket: aliOssConfig.bucket,
    });
    await ossClient.listV2({
      prefix: '/',
      delimiter: "/",
    }, {
      timeout: 5000,
    });
  }
}
