import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "ali-oss",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "阿里云 OSS",
    pluginDesc: "阿里云的对象存储",
    pluginVersion: "1.0.6",
    pluginGroup: 'cloudVendor'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: '访问 id',
          field: 'accessKeyId',
          rules: [
            {
              required: true,
              message: '访问 id 必填',
            }
          ]
        },
        elementProperty: {
          placeholder: 'accessKeyId'
        }
      },
      {
        type: 'input',
        formItem: {
          label: '密钥',
          field: 'accessKeySecret',
          rules: [
            {
              required: true,
              message: '访问 id 必填',
            }
          ]
        },
        elementProperty: {
          placeholder: 'accessKeySecret',
          type: 'password'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '地域',
          field: 'region',
          rules: [
            {
              required: true,
              message: '地域 ID 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'Region',
        },
      },
      {
        type: 'input',
        formItem: {
          label: '桶名称',
          field: 'bucket',
          rules: [
            {
              required: true,
              message: 'Bucket 名称',
            }
          ]
        },
        elementProperty: {
          placeholder: 'Bucket',
        },
      },
      {
        type: 'input-format',
        formItem: {
          label: '文件路径',
          field: 'filePath',
          rules: [
            {
              required: true,
              message: '请输入文件路径',
            }
          ]
        },
        elementProperty: {
          placeholder: '请输入文件路径',
          defaultValue: '{filename}.{suffix}'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '自定义域名',
          field: 'domain',
          rules: [
            {
              validator: async (value: any | undefined) => {
                if (!value) {
                  return true;
                }
                if (value.toString().startsWith('http://') || value.toString().startsWith('https://')) {
                  return true;
                }
                return { result: false, message: '自定义域名需要 http 和 https 开头', type: 'warning' };
              }
            }
          ]
        },
        elementProperty: {
        },
      },
      {
        type: 'input',
        formItem: {
          label: '图片样式',
          field: 'style',
        },
        elementProperty: {
        },
      },
    ]
  }
});
