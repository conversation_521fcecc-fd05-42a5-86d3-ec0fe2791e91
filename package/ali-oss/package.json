{"name": "ali-oss", "version": "1.0.0", "description": "", "main": "src/app.ts", "scripts": {"build": "vite build && cd dist", "build:dev": "vite build -c vite.dev.config.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/ali-oss": "^6.16.11", "@types/node": "^20.12.5", "rollup-plugin-copy": "^3.5.0", "ts-node": "^10.9.2", "typescript": "^5.7.3", "vite": "^6.0.11"}, "dependencies": {"@xiaou66/picture-plugin": "^0.0.36", "ali-oss": "^6.22.0"}}