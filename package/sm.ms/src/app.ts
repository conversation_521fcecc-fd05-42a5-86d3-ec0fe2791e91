import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";

const API_ENDPOINT = 'https://sm.ms/api/v2'


export interface ISmMsConfig {
  token: string;
  fileNameTemplate?: string;
}

export interface ISmMsExtra {
  hash: string;
  path: string;
}


interface SmMsRequest<T = any> {
  success: boolean;
  code: string;
  message: string;
  data?: T;
  RequestId: string;
}

interface ImageFile {
  file_id: number;
  width: number;
  height: number;
  filename: string;
  storename: string;
  size: number;
  path: string;
  hash: string;
  url: string;
  delete: string;
  page: string;
}



/**
 * 导出插件
 */
export default class CatBoxStoragePlugIn extends StoragePlugIn<ISmMsConfig, ISmMsExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }

  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<ISmMsExtra>> {
    const config = this.readConfig(params.storageId);
    if (!config.token) {
      throw new Error('未填写 token 值');
    }
    const formData = new FormData();

    let fileName = params.allFileName;
    if (config.fileNameTemplate) {
      fileName = this.formatUploadPath(config.fileNameTemplate, params);
    }

    formData.append('smfile', new File([params.file], fileName));
    formData.append('format', "json");
    const res: SmMsRequest<ImageFile> = await fetch(`${API_ENDPOINT}/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${config.token}`,
      },
      body: formData
    }).then(res => res.json());

    if (!res.success || !res.data) {
      throw new Error(res.message);
    }

    return  {
      url: res.data.url,
      extra: {
        hash: res.data.hash,
        path: res.data.path,
      }
    }
  }

  /**
   * 删除图片
   * @param params
   */
  async deleteFile(params: IDeleteFileParams<ISmMsExtra>): Promise<boolean> {
    const config = this.readConfig(params.storageId);
    if (!config.token) {
      throw new Error('未填写 token 值');
    }

    if (!params.extra) {
      throw new Error('缺少必要信息');
    }

    await fetch(`${API_ENDPOINT}/delete/${params.extra.hash}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${config.token}`,
      }
    });
    return true;
  }

  async verifyConfig(config: ISmMsConfig): Promise<void> {
    const res: SmMsRequest = await fetch(`${API_ENDPOINT}/profile`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${config.token}`,
      }
    }).then(res => res.json());
    if (!res.success) {
      throw new Error(res.message);
    }
  }
}
