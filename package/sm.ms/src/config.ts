import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "sm.ms",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "sm.ms",
    pluginDesc: "一个免费的图床服务，允许用户上传、存储和分享图片",
    pluginVersion: "1.0.3",
    pluginGroup: 'other'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: 'token',
          field:  'token',
          rules: {
            required: true,
            message: 'token 不能为空',
          }
        },
        elementProperty: {
          placeholder: 'token',
          type: 'password'
        },
      },
      {
        type: 'input-format',
        formItem: {
          type: 'file',
          label: '文件名',
          field: 'fileNameTemplate',
          rules: {
            validator: (value: any | undefined, callback: (error?: string) => void) => {
              if (!value) {
                return;
              }
              if (!value.toString().includes('/') && !value.toString().includes('\\') ) {
                return;
              }
              callback('文件名称不支持使用目录分割符');
            }
          }
        },
        elementProperty: {
          placeholder: '请输入文件名称',
          defaultValue: '{filename}.{suffix}'
        },
      },
    ]
  }
});
