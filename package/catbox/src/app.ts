import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";

const CATBOX_API_ENDPOINT = 'https://catbox.moe/user/api.php'


export interface ICatBoxConfig {
  userHash?: string;
  fileName?: string;
}

export interface ICatBoxExtra {
  key: string;
}

/**
 * 导出插件
 */
export default class CatBoxStoragePlugIn extends StoragePlugIn<ICatBoxConfig, ICatBoxExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }

  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<ICatBoxExtra>> {
    const catBoxConfig = this.readConfig(params.storageId);

    const data = new FormData();
    data.set('reqtype', 'fileupload');
    data.set('fileToUpload', params.file);

    if (catBoxConfig.userHash) {
      data.set('userhash', catBoxConfig.userHash);
    }

    const res = await this._doRequest(data);
    if (res.startsWith('https://files.catbox.moe/')) {
      return {
        url: res,
        extra: {
          key: res.replace('https://files.catbox.moe/', ''),
        }
      } as IUploadFileResult;
    } else {
      throw new Error(res);
    }
  }

  /**
   * 删除图片
   * @param params
   */
  async deleteFile(params: IDeleteFileParams<ICatBoxExtra>): Promise<boolean> {
    const catBoxConfig = this.readConfig(params.storageId);
    if (!catBoxConfig.userHash || !params.extra) {
      throw new Error('未填写 userHash 值');
    }

    const data = new FormData();
    data.set('reqtype', 'deletefiles');
    data.set('userhash',  catBoxConfig.userHash);
    data.set('files', params.extra.key);

    const res = await this._doRequest(data);
    if (res.includes('successfully')) {
      return true;
    } else {
      throw new Error(res);
    }
  }

  private async _doRequest(data: FormData): Promise<string> {
    const init: RequestInit = {
      method: 'POST',
      headers: {
        'user-agent': 'depthbomb/node-catbox',
      },
      body: data
    };
    const res = await fetch(CATBOX_API_ENDPOINT, init);
    return res.text();
  }
}
