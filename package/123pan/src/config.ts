import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "123pan",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "123 云盘",
    pluginDesc: "需要开通会员, 123云盘是一款注册即享免费2T超大存储空间、上传下载不限速、支持海量文件管理的网盘储存安全平台",
    pluginVersion: "1.0.2",
    pluginGroup: 'other',
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: 'clientId',
          field: 'clientId',
          rules: [
            {
              required: true,
              message: 'clientId 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'client_id',
        },
      },
      {
        type: 'input',
        formItem: {
          label: 'clientSecret',
          field:  'clientSecret',
          rules: [
            {
              required: true,
              message: 'client_secret 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'client_secret',
          type: 'password'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '文件夹 id',
          field:  'uploadFleId',
          rules: [
            {
              required: true,
              message: 'uploadFleId 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'uploadFleId'
        },
      },
      {
        type: 'input-format',
        formItem: {
          type: 'file',
          label: '文件名格式',
          field: 'fileNameTemplate',
          rules: [
            {
              validator: async (value: any | undefined) => {
                if (!value) {
                  return true;
                }
                if (!value.toString().includes('/') && !value.toString().includes('\\') ) {
                  return true;
                }
                return { result: false, message: '文件名称不支持使用目录分割符', type: 'warning' };
              }
            }
          ]
        },
        elementProperty: {
          placeholder: '请输入文件名称',
          defaultValue: '{filename}_{timestamp}.{suffix}'
        },
      },
    ]
  }
});
