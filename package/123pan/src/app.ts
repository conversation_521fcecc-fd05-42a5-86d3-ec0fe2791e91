import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";
import api, {getDirList, Token} from "./api";

const DEFAULT_FILE_NAME_FORMAT = "{filename}_{timestamp}.{suffix}";


export interface IConfig {
  clientId: string;
  clientSecret: string;
  uploadFleId: string;
  fileNameTemplate?: string;
}

export interface IExtra {
  fileId: string;
}


/**
 * 导出插件
 */
export default class CatBoxStoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {

  }

  protected doDestroy() {
  }



  async uploadFileInner(preUploadId: string, file: File, sliceSize: number, token: Token): Promise<string> {
    // 使用一个更可靠的方式上传所有分片
    const chunkSize = sliceSize;
    const chunks = Math.ceil(file.size / chunkSize);
    const blobSlice = File.prototype.slice;

    // 上传所有分片
    for (let chunkIndex = 0; chunkIndex < chunks; chunkIndex++) {
      const start = chunkIndex * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = blobSlice.call(file, start, end);

      try {
        // 获取上传URL
        const uploadUrl = await api.getUploadUrl(preUploadId, chunkIndex + 1, token);
        // 上传分片
        const uploadResponse = await fetch(uploadUrl, {
          method: 'PUT',
          body: chunk
        });
        if (!uploadResponse.ok) {
          throw new Error(`分片 ${chunkIndex + 1} 上传失败: ${uploadResponse.status}`);
        }

        console.log(`分片 ${chunkIndex + 1}/${chunks} 上传成功`);
      } catch (error) {
        console.error(`分片 ${chunkIndex + 1} 上传错误:`, error);
        throw error;
      }
    }

    console.log('所有分片上传完成，等待服务器处理...');

    // 等待一段时间再检查上传状态，给服务器一些处理时间
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 完成上传
    await api.uploadComplete(preUploadId, token);

    // 检查上传状态
    const data = await api.uploadAsyncResult(preUploadId, token);

    if (!data || !data.fileID) {
      throw new Error('未获取到有效的 fileID');
    }

    return data.fileID;
  }

  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    const config = this.readConfig(params.storageId);
    const token = await api.getAccessToken(config.clientId, config.clientSecret);
    const md5 = await api.calcMD5(params.file);
    const fileName = this.formatUploadPath(config.fileNameTemplate || DEFAULT_FILE_NAME_FORMAT, params);
    const res = await api.createFile(params.file, fileName, md5, config.uploadFleId, token);
    let fileId;
    if (res.reuse) {
      // 秒传成功，会直接返回文件 ID
      fileId = res.fileID;
    } else {
      fileId = await this.uploadFileInner(res.preuploadID, params.file, res.sliceSize, token);
    }

    // 获取直接链接
    const directLink = await api.getDirectLink(fileId, token);

    return {
      url: directLink,
      extra: {
        fileId,
      }
    };
  }

  /**
   * 删除图片
   * @param params
   */
  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    if (!params.extra) {
      return false;
    }
    const config = this.readConfig(params.storageId);
    const token = await api.getAccessToken(config.clientId, config.clientSecret);
    await api.deleteToTrash([params.extra.fileId], token);
    return true;
  }

  async verifyConfig(config: IConfig): Promise<void> {
    const token = await api.getAccessToken(config.clientId, config.clientSecret);
  }
}
