const HOST = 'https://open-api.123pan.com';
// @ts-ignore
import SparkMD5 from 'spark-md5'

export interface Token {
  expiredAt: number;
  accessToken: string;
}


const tokenPool: Map<string, Token> = new Map();

/**
 * 获取访问令牌
 *
 * @param clientId 客户端ID
 * @param clientSecret 客户端密钥
 * @returns 返回一个包含访问令牌和过期时间的对象，或抛出错误
 * @throws 如果请求失败或响应码不为0，则抛出错误
 */
async function getAccessToken(clientId: string, clientSecret: string): Promise<Token> {
  const token = tokenPool.get(clientId);
  console.log('token', token)
  if (token && Date.now() < token.expiredAt) {
    return token;
  }
  const newToken = await fetch(HOST + '/api/v1/access_token', {
    method: 'POST',
    body: JSON.stringify({
      clientID: clientId,
      clientSecret
    }),
    headers: {
      Authorization: '',
      Platform: 'open_platform'
    }
  }).then(res => res.json())
    .then(res => {
      if (res.code !== 0) {
        throw new Error(res.message);
      }
      return {
        expiredAt: new Date(res.data.expiredAt).getTime(),
        accessToken: res.data.accessToken as string
      }
    });
  tokenPool.set(clientId, newToken);
  return newToken;
}

async function createFile(file: File, filename: string, etag: string, parentFileId: string, token: Token) {
  const parentFileID = parentFileId;
  return await fetch(HOST + '/upload/v1/file/create', {
    method: 'POST',
    headers: {
      Authorization: token.accessToken,
      Platform: 'open_platform'
    },
    body: JSON.stringify({
      parentFileID,
      filename,
      etag,
      size: file.size
    })
  }).then(res => res.json())
    .then(res => res.data)
}


async function getUploadUrl (preuploadID: string, sliceNo: number, token: Token) {
  return await fetch(HOST + '/upload/v1/file/get_upload_url', {
    method: 'POST',
    headers: {
      Authorization: token.accessToken,
      Platform: 'open_platform'
    },
    body: JSON.stringify({
      preuploadID,
      sliceNo
    })
  }).then(res => res.json())
    .then(res => res.data.presignedURL)
}

async function uploadAsyncResult(preuploadID: string, token: Token): Promise<any> {
  return new Promise((resolve, reject) => {
    let attempts = 0;
    const maxAttempts = 30; // 最多尝试30次

    console.log('开始检查上传状态...');

    const interval = setInterval(async () => {
      attempts++;
      try {
        console.log(`第${attempts}次检查上传状态...`);

        const res = await fetch(HOST + '/upload/v1/file/upload_async_result', {
          method: 'POST',
          headers: {
            Authorization: token.accessToken,
            Platform: 'open_platform'
          },
          body: JSON.stringify({ preuploadID })
        }).then(res => res.json());

        console.log('状态检查结果:', JSON.stringify(res));

        if (res.code !== 0) {
          clearInterval(interval);
          reject(new Error(`上传状态检查失败: ${res.message}`));
          return;
        }

        if (res.data.completed && res.data.fileID !== 0) {
          console.log('上传已完成，fileID:', res.data.fileID);
          clearInterval(interval);
          resolve(res.data);
          return;
        }

        // 超过最大尝试次数
        if (attempts >= maxAttempts) {
          clearInterval(interval);
          reject(new Error(`超过最大检查次数(${maxAttempts})，上传可能未成功完成`));
          return;
        }

        console.log('上传尚未完成，等待下次检查...');
      } catch (e) {
        console.error('检查上传状态出错:', e);
        clearInterval(interval);
        reject(e);
      }
    }, 2000); // 增加间隔到2秒

    // 添加超时处理
    setTimeout(() => {
      clearInterval(interval);
      reject(new Error('上传结果查询超时（5分钟）'));
    }, 300000); // 5分钟超时
  });
}

async function uploadComplete(preuploadID: string, token: Token) {
  try {
    console.log('调用上传完成接口...');
    const response = await fetch(HOST + '/upload/v1/file/upload_complete', {
      method: 'POST',
      headers: {
        Authorization: token.accessToken,
        Platform: 'open_platform'
      },
      body: JSON.stringify({
        preuploadID
      })
    });

    const data = await response.json();
    console.log('上传完成接口返回:', JSON.stringify(data));

    if (data.code !== 0) {
      throw new Error(`上传完成失败: ${data.message}`);
    }

    return data.data;
  } catch (error) {
    console.error('上传完成接口调用失败:', error);
    throw error;
  }
}

async function getDirectLink (fileID: string, token: Token) {
  return await fetch(HOST + '/api/v1/direct-link/url?fileID=' + fileID, {
    headers: {
      Authorization: token.accessToken,
      Platform: 'open_platform'
    }
  }).then(res => res.json())
    .then(res => {
      if(res.code ===0) {
        return res.data.url;
      }
      throw new Error(res.message);
    });
}

function loadNext (fileReader: FileReader, file: File, currentChunk: number, chunkSize: number) {
  const blobSlice = File.prototype.slice
  const start = currentChunk * chunkSize
  const end = ((start + chunkSize) >= file.size) ? file.size : start + chunkSize
  return fileReader.readAsArrayBuffer(blobSlice.call(file, start, end))
}

function calcMD5 (file: File): Promise<string> {
  const blobSlice = File.prototype.slice;
  const chunkSize = 2097152 // Read in chunks of 2MB
  const chunks = Math.ceil(file.size / chunkSize)
  let currentChunk = 0
  const spark = new SparkMD5.ArrayBuffer()
  const fileReader = new FileReader()

  function loadNext () {
    const start = currentChunk * chunkSize
    const end = ((start + chunkSize) >= file.size) ? file.size : start + chunkSize
    fileReader.readAsArrayBuffer(blobSlice.call(file, start, end))
  }
  return new Promise(resolve => {
    fileReader.onload = function (e) {
      console.log('read chunk nr', currentChunk + 1, 'of', chunks)
      spark.append(e.target!.result) // Append array buffer
      currentChunk++

      if (currentChunk < chunks) {
        loadNext()
      } else {
        console.log('finished loading')
        resolve(spark.end())
      }
    }

    fileReader.onerror = function () {
      console.warn('oops, something went wrong.')
    }
    loadNext()
  })
}

export async function getDirList (searchMode: string, token: Token): Promise<any> {
  // https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/zrip9b0ye81zimv4
  return await fetch(HOST + '/api/v1/file/list?parentFileId=0&page=1&limit=100&orderBy=size&orderDirection=asc', {
    headers: {
      Authorization: token.accessToken,
      Platform: 'open_platform'
    }
  }).then(res => res.json())
    .then(res => res.data)
}


/**
 * 将文件移动到回收站
 *
 * @param fileIds 文件 ids
 * @param token 认证令牌
 */
async function deleteToTrash(fileIds: string[], token: Token) {
  return await fetch(HOST + '/api/v1/file/trash', {
    method: 'POST',
    headers: {
      Authorization: token.accessToken,
      Platform: 'open_platform'
    },
    body: JSON.stringify({
      fileIDs: fileIds,
    })
  }).then(res => res.json())
    .then(res => {
      if (res.code !== 0) {
        throw new Error(res.message);
      }
    })
}

export default {
  getAccessToken,
  calcMD5,
  createFile,
  getUploadUrl,
  loadNext,
  uploadAsyncResult,
  uploadComplete,
  getDirectLink,
  getDirList,
  deleteToTrash
}
