{"name": "123pan", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "vite build", "build:dev": "vite build -c vite.dev.config.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "private": true, "devDependencies": {"rollup-plugin-copy": "^3.5.0", "ts-node": "^10.9.2", "typescript": "^5.7.3", "vite": "^6.0.11"}, "dependencies": {"@xiaou66/picture-plugin": "^0.0.36", "spark-md5": "^3.0.2"}}