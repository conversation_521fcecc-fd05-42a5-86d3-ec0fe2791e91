import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";
import { lib, enc } from 'crypto-js';
const DEFAULT_FILE_PATH_FORMAT = "{YY}-{M}/{filename}_{timestamp}.{suffix}";

export interface IConfig {
  accessToken: string;
  owner: string;
  repo: string;
  filePath: string;
  branch?: string;
}

export interface IExtra {
  sha: string;
  path: string;
}


interface UploadRequest<T = any> {

}

interface FileResponse {
}




/**
 * 导出插件
 */
export default class CheveretoStoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }

  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    const config = this.readConfig(params.storageId);
    // 生成文件路径
    const uploadPath = this.formatUploadPath(config.filePath || DEFAULT_FILE_PATH_FORMAT, params);

    // 将文件内容转换为 Base64
    const fileContent = await params.file.arrayBuffer();
    const wordArray = lib.WordArray.create(fileContent);
    const content = enc.Base64.stringify(wordArray);

    const body = {
      access_token: config.accessToken,
      content,
      message: '图床 Plus'
    }
    const res = await fetch(`https://gitee.com/api/v5/repos/${config.owner}/${config.repo}/contents/${uploadPath}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    }).then(res => res.json());
    if (res.content) {
      return {
        url: res.content.download_url,
        extra: {
          sha: res.content.sha,
          path: res.content.path,
        }
      }
    } else {
      if (res && res.message) {
        throw new Error(res.message);
      }
      throw new Error("上传出现问题");
    }
  }

  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    if (!params.extra) {
      throw new Error('缺少必要参数');
    }
    const config = this.readConfig(params.storageId);
    const apiPrefix = 'https://gitee.com/api/v5/repos/';
    const res = await fetch(`${apiPrefix}${config.owner}/${config.repo}/contents/${params.extra.path}?sha=${params.extra.sha}&message=删除${params.extra.path}文件&access_token=${config.accessToken}`, {
      method: 'DELETE',
    })
      .then(res => res.json());
    if (res.commit) {
      return true;
    } else {
      if (res && res.message) {
        throw new Error(res.message);
      }
      throw new Error("删除出现问题");
    }
  }



  async verifyConfig(config: IConfig): Promise<void> {
    console.log('config', config)
    const res = await fetch(`https://gitee.com/api/v5/repos/${config.owner}/${config.repo}/branches/${config.branch || 'master'}`, {
      headers: {
        'access_token': config.accessToken,
      }
    }).then(res => res.json());

    if (res.message && res.name !== config.branch) {
      throw new Error(res.message);
    }

    if (!res.commit.author || res.commit.author.login !== config.owner) {
      throw new Error("必须是自己拥有的仓库");
    }
  }
}
