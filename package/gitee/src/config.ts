import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "gitee",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "Gitee",
    pluginDesc: "国内领先的企业级研发效能和开源代码托管平台",
    pluginVersion: "1.0.4",
    pluginGroup: 'other'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: '令牌',
          field:  'accessToken',
          rules: [
            {
              required: true,
              message: 'access_token 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'access_token',
          type: 'password'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '拥有者',
          field:  'owner',
          tooltip: '仓库所属空间地址(企业、组织或个人)',
          rules: [
            {
              required: true,
              message: 'owner 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'owner',
        },
      },
      {
        type: 'input',
        formItem: {
          label: '仓库名',
          field:  'repo',
          rules: [
            {
              required: true,
              message: 'repo 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'repo',
        },
      },
      {
        type: 'input-format',
        formItem: {
          label: '文件路径',
          field: 'filePath',
          rules: [
            {
              required: true,
              message: '请输入文件路径名称',
            }
          ]
        },
        elementProperty: {
          placeholder: '请输入文件路径名称',
          defaultValue: '{YY}-{M}/{filename}_{timestamp}.{suffix}'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '分支',
          field:  'branch',
          rules: [
            {
              message: 'branch 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'branch',
          defaultValue: 'master'
        },
      },
    ]
  }
});
