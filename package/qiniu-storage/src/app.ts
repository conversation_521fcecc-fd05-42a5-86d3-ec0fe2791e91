import {IDeleteFileParams, IUploadFileParams, IUploadFileResult, StoragePlugIn} from "@xiaou66/picture-plugin";
import {generateUploadToken, getBucketList, deleteFile} from "./utils";
import * as qiniu from 'qiniu-js'

interface IConfig {
  region: string;
  accessKey: string;
  secretKey: string;
  bucket: string;
  filePath: string;
  domain: string;
}

interface IExtra {
  key: string;
  hash: string;
}


const DEFAULT_FILE_PATH_FORMAT = "{YY}-{M}/{filename}_{timestamp}.{suffix}";

/**
 * 导出插件
 */
export default class QiniuStoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }

  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    const config = this.readConfig(params.storageId);
    const fileName = this.formatUploadPath(config.filePath || DEFAULT_FILE_PATH_FORMAT, params);
    return new Promise((resolve, reject) => {
      const uploadTask = qiniu.createDirectUploadTask({
        type: 'file',
        data: new File([params.file], fileName),
      }, {
        tokenProvider: () => {
          const res = generateUploadToken({
            assessKey: config.accessKey,
            secretKey: config.secretKey,
            bucketName: config.bucket,
            deadline: (Date.now() / 1000) + (60 * 30)
          });
          return Promise.resolve(res);
        }
      });
      uploadTask.onComplete((a) => {
        if (a) {
          const obj = JSON.parse(a);
          const domain = config.domain.endsWith("/") ? config.domain : config.domain + '/';
          resolve({
            url: domain + obj.key,
            extra: {
              key: obj.key,
              hash: obj.hash,
            }
          });
        }
      });
      uploadTask.onError((err) => {
        if (err) {
          reject(err.message);
        }
      });
      uploadTask.start().then(() => {});
    });
  }


  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    if (!params.extra) {
      throw new Error('缺少必要参数');
    }
    const config = this.readConfig(params.storageId);
    await deleteFile(config.accessKey, config.secretKey, config.bucket, params.extra.key);
    return true;
  }

  async verifyConfig(config: IConfig): Promise<void> {
    if (!config.accessKey || !config.secretKey || !config.bucket) {
      throw new Error('配置无效：accessKey、secretKey 和 bucket 为必填项')
    }
    const bucketList = await getBucketList(config.accessKey, config.secretKey);
    if (!bucketList.includes(config.bucket)) {
      throw new Error("bucket 不存在");
    }
  }

  private getClient(config: IConfig) {
  }
}
