import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "qiniu-storage",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "七牛云存储",
    pluginDesc: "是七牛云提供的高可靠、强安全、低成本、可扩展的存储服务",
    pluginVersion: "1.0.3",
    pluginGroup: 'cloudVendor'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: '域名',
          field: 'domain',
          tooltip: '一定要使用自定义域名, 分配的域名 30 天后会过期',
          rules: [
            {
              required: true,
              message: '域名不能为空',
            },
            {
              validator: (value: any | undefined, callback: (error?: string) => void) => {
                if (!value) {
                  return;
                }
                if (value.toString().startsWith('http://') || value.toString().startsWith('https://')) {
                  return;
                }
                callback('自定义域名需要 http 和 https 开头');
              }
            }
          ]
        },
        elementProperty: {
          placeholder: '一定要使用自定义域名, 分配的域名 30 天后会过期'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '访问 key(AK)',
          field: 'accessKey',
          rules: {
            required: true,
            message: '访问 key 必填',
          }
        },
        elementProperty: {
          placeholder: 'accessKey'
        }
      },
      {
        type: 'input',
        formItem: {
          label: '访问密匙(SK)',
          field:  'secretKey',
          rules: {
            required: true,
            message: '密匙不能为空',
          }
        },
        elementProperty: {
          placeholder: 'secretKey',
          type: 'password'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '桶名称',
          field: 'bucket',
          rules: {
            required: true,
            message: 'Bucket 名称',
          }
        },
        elementProperty: {
          placeholder: 'Bucket',
        },
      },
      {
        type: 'input-format',
        formItem: {
          label: '文件路径',
          field: 'filePath',
        },
        elementProperty: {
          placeholder: '请输入文件路径',
          defaultValue: '{YY}-{M}/{filename}_{timestamp}.{suffix}'
        },
      },
    ]
  }
});
