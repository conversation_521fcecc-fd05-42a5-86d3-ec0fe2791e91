import CryptoJS from 'crypto-js'

function base64UrlSafeEncode(target: string): string {
  return target.replace(/\//g, '_').replace(/\+/g, '-')
}


interface TokenOptions {
  assessKey?: string
  secretKey?: string
  bucketName?: string
  deadline?: number
}

function generateUploadToken(options: Required<TokenOptions>) {
  const { deadline, bucketName, assessKey, secretKey } = options

  // 构建上传策略
  const putPolicy = JSON.stringify({
    scope: bucketName,
    deadline: Math.floor(deadline) // 确保 deadline 是整数
  })

  // Base64 编码上传策略
  const encodedPutPolicy = base64UrlSafeEncode(
    CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(putPolicy))
  )

  // 计算HMAC-SHA1签名
  const sign = base64UrlSafeEncode(
    CryptoJS.HmacSHA1(encodedPutPolicy, secretKey).toString(CryptoJS.enc.Base64)
  )

  // 拼接上传凭证
  return `${assessKey}:${sign}:${encodedPutPolicy}`
}

interface RequestOptions {
  method: string;
  path: string;
  host: string;
  contentType?: string;
  body?: string | Buffer;
  headers?: Record<string, string>;
}

function generateAccessToken(options: RequestOptions, secretKey: string, accessKey: string): string {
  const { method, path, host, contentType = 'application/x-www-form-urlencoded', body, headers } = options

  // 1. 构建签名字符串的起始部分
  let signingStr = `${method.toUpperCase()} ${path}`

  // 2. 添加 Host
  signingStr += `\nHost: ${host}`

  // 3. 添加 Content-Type
  signingStr += `\nContent-Type: ${contentType}`

  // 4. 添加自定义头部（如果有的话）
  if (headers) {
    const xQiniuHeaders = Object.entries(headers)
      .filter(([key]) => key.toLowerCase().startsWith('x-qiniu-'))
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n')

    if (xQiniuHeaders) {
      signingStr += '\n' + xQiniuHeaders
    }
  }

  // 5. 添加两个换行符
  signingStr += '\n\n'

  // 6. 添加请求体（如果有且不是二进制流）
  if (body && contentType !== 'application/octet-stream') {
    signingStr += body
  }

  // 7. 计算 HMAC-SHA1 签名
  const sign = CryptoJS.HmacSHA1(signingStr, secretKey)
  const encodedSign = base64UrlSafeEncode(
    CryptoJS.enc.Base64.stringify(sign)
  )

  // 8. 拼接并返回完整的认证token
  return `${accessKey}:${encodedSign}`
}

async function getBucketList(accessKey: string, secretKey: string): Promise<string[]> {
  const accessToken = generateAccessToken({
    method: 'GET',
    path: '/buckets',
    host: 'uc.qiniuapi.com',
    contentType: 'application/x-www-form-urlencoded'
  }, secretKey, accessKey)

  try {
    const response = await fetch('https://uc.qiniuapi.com/buckets', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Qiniu ${accessToken}`
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`${response.status} ${errorText}`)
    }

    return await response.json();
  } catch (error) {
    throw error
  }
}

async function deleteFile(accessKey: string, secretKey: string, bucket: string, key: string): Promise<void> {
  // 1. 构建 entry (对 key 进行 URL 编码后再进行 base64 编码)
  const entry = base64UrlSafeEncode(
    Buffer.from(`${bucket}:${key}`).toString('base64')
  )

  const host = 'rs.qiniu.com'
  const path = `/delete/${entry}`
  const accessToken = generateAccessToken({
    method: "POST",
    path: path,
    host,
    contentType: 'application/x-www-form-urlencoded'
  },secretKey, accessKey);

  try {
    const response = await fetch(`https://${host}${path}`, {
      method: 'POST',
      headers: {
        'Authorization': `Qiniu ${accessToken}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`删除文件失败: ${response.status} ${errorText}`)
    }
  } catch (error) {
    throw error
  }
}

export {
  getBucketList,
  generateUploadToken,
  generateAccessToken,
  deleteFile
}
