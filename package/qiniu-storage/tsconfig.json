{
  "compilerOptions": {
    "target": "ES6",             // 目标 ECMAScript 版本
    "module": "CommonJS",        // 使用 CommonJS 模块
    "outDir": "./dist",          // 输出目录
    "rootDir": "./src",          // 源代码目录
    "strict": true,              // 启用所有严格类型检查选项
    "esModuleInterop": true       // 允许默认导入非 ES 模块
  },
  "include": ["src/**/*"],        // 编译包含的文件
  "exclude": ["node_modules"]     // 排除的文件
}
