import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";
import {AuthType, createClient, WebDAVClient} from "webdav";
const DEFAULT_FILE_PATH_FORMAT = "{YY}-{M}/{filename}_{timestamp}.{suffix}";
const DEFAULT_MOUNT_PATH = '/';
export interface IConfig {
  serviceUrl: string;
  username: string;
  password: string;
  filePath: string;
  authType: 'Auto' | 'Digest' | 'Password' | 'Token';
  directLinkPrefix: string;
  mountPath?: string;
}

export interface IExtra {
  filePath?: string;
  originalFilePath?: string;
}


interface FileResponse {
  ok: boolean;
  src: string;
}


/**
 * 导出插件
 */
export default class Image16StoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }

  private buildFilePath(config: IConfig, fileName: string): string {
    const mountPath = config.mountPath || DEFAULT_MOUNT_PATH;
    return mountPath.endsWith('/')
      ? `${mountPath}${fileName}`
      : `${mountPath}/${fileName}`;
  }

  private normalizePath(path: string): string {
    let normalized = path.startsWith('/') ? path.slice(1) : path;
    return normalized.endsWith('/') && normalized !== '' ? normalized.slice(0, -1) : normalized;
  }

  private async ensureDirectoryExists(client: WebDAVClient, filePath: string): Promise<void> {
    const pathParts = filePath.split('/').filter(part => part.length > 0);
    if (pathParts.length <= 1) {
      return;
    }

    const dirPath = pathParts.slice(0, -1).join('/');
    if (!dirPath) {
      return;
    }

    try {
      const dirExists = await client.exists(`/${dirPath}`);
      if (!dirExists) {
        console.log(`WebDAV: creating directory: /${dirPath}`);
        await client.createDirectory(`/${dirPath}`, { recursive: true });
      }
    } catch (error: any) {
      console.warn(`WebDAV: error creating directory /${dirPath}:`, error.message);
    }
  }

  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    const config = this.readConfig(params.storageId);
    const client = this.getWebDavClient(config);

    const fileName = this.formatUploadPath(config.filePath || DEFAULT_FILE_PATH_FORMAT, params);
    const filePath = this.buildFilePath(config, fileName);
    const normalizedFilePath = this.normalizePath(filePath);

    console.log(`WebDAV upload: attempting to upload to: /${normalizedFilePath}`);

    const fileBuffer = await params.file!.arrayBuffer();

    try {
      await this.ensureDirectoryExists(client, normalizedFilePath);

      await client.putFileContents(`/${normalizedFilePath}`, fileBuffer, { overwrite: true });

      const fileUrl = `/${normalizedFilePath}`;
      const url = `${config.directLinkPrefix}${fileUrl}`;

      console.log(`WebDAV upload: successfully uploaded to: ${url}`);

      return {
        url,
        extra: {
          filePath: `/${normalizedFilePath}`,
          originalFilePath: filePath
        }
      };
    } catch (error: any) {
      console.error('WebDAV upload error:', error);

      if (error.message && error.message.includes('does not exist')) {
        throw new Error(`上传失败: 目标路径不存在或没有权限访问。请检查 mountPath 配置和服务器权限。路径: /${normalizedFilePath}`);
      }

      throw new Error(`上传失败: ${error.message}`);
    }
  }

  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    try {
      if (!params.extra || !params.extra.filePath) {
        console.warn('WebDAV delete: missing filePath in extra data');
        return false;
      }

      const config = this.readConfig(params.storageId);
      const client = this.getWebDavClient(config);

      let filePath = params.extra.filePath;
      if (!filePath.startsWith('/')) {
        filePath = `/${filePath}`;
      }

      console.log(`WebDAV delete: attempting to delete: ${filePath}`);

      try {
        const fileExists = await client.exists(filePath);
        if (!fileExists) {
          console.warn(`WebDAV delete: file not found: ${filePath}`);
          return true;
        }
      } catch (checkError: any) {
        console.warn(`WebDAV delete: error checking file existence: ${checkError.message}`);
      }

      await client.deleteFile(filePath);
      console.log(`WebDAV delete: successfully deleted: ${filePath}`);
      return true;
    } catch (error: any) {
      console.error('WebDAV delete error:', error);

      if (error.status === 409) {
        throw new Error(`删除失败 (409 Conflict): 文件可能被锁定或正在使用中。路径: ${params.extra?.filePath}`);
      }

      if (error.status === 404 || (error.message && error.message.includes('does not exist'))) {
        console.warn('WebDAV delete: file not found, considering delete successful');
        return true;
      }

      throw new Error(`删除失败: ${error.message}`);
    }
  }

  async verifyConfig(config: IConfig): Promise<void> {
    try {
      const client = this.getWebDavClient(config);

      await client.getDirectoryContents("/");

      if (config.mountPath && config.mountPath !== '/') {
        const mountPath = config.mountPath.startsWith('/') ? config.mountPath : `/${config.mountPath}`;
        try {
          await client.getDirectoryContents(mountPath);
          console.log(`WebDAV config: mountPath ${mountPath} is accessible`);
        } catch (mountError: any) {
          console.warn(`WebDAV config: mountPath ${mountPath} may not exist or be accessible:`, mountError.message);
        }
      }

      return;
    } catch (error: any) {
      console.error('WebDAV config verification error:', error);
      throw new Error(`配置验证失败: ${error.message}`);
    }
  }

  getWebDavClient(config: IConfig): WebDAVClient {
    return createClient(config.serviceUrl, {
      authType: AuthType[config.authType],
      username: config.username,
      password: config.password
    });
  }

}
