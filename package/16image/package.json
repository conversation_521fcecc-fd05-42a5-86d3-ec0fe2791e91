{"name": "16image", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "vite build", "build:dev": "vite build -c vite.dev.config.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "private": true, "devDependencies": {"@types/node": "^20.12.5", "rollup-plugin-copy": "^3.5.0", "ts-node": "^10.9.2", "typescript": "^5.7.3", "utools-api-types": "^6.1.0", "vite": "^6.0.11"}, "dependencies": {"@xiaou66/picture-plugin": "^0.0.36", "nanoid": "^5.0.9"}}