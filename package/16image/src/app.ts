import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";
import config from "./config";
import {nanoid} from "nanoid";
const DEFAULT_FILE_PATH_FORMAT = "{filename}.{suffix}";
export interface IConfig {
  token?: string;
}

export interface IExtra {
  key: string;
}


interface FileResponse {
  ok: boolean;
  src: string;
}


/**
 * 导出插件
 */
export default class Image16StoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }


  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    const config = this.readConfig(params.storageId);
    const formData = new FormData();
    formData.append("image", new File([params.file], params.allFileName));
    const res: FileResponse = await fetch(`https://i.111666.best/image`, {
      method: 'POST',
      body: formData,
      headers: {
        'auth-token': config.token || '',
      }
    }).then(res => res.json());
    if (!res.ok) {
      throw Error(JSON.stringify(res));
    }


    return {
      url: `https://i.111666.best${res.src}`,
      extra: {
        key: res.src,
      }
    };
  }

  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    if (!params.extra) {
      throw new Error('缺少必要参数');
    }

    const config = this.readConfig(params.storageId);
    await fetch(`https://i.111666.best${params.extra.key}`, {
      method: 'DELETE',
      headers: {
        'auth-token': config.token || '',
      }
    });

    return true;
  }

  async verifyConfig(config: IConfig): Promise<void> {
    if (!config.token) {
      config.token = nanoid(32);
    }
    return;
  }

}
