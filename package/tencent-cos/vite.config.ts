import { defineConfig } from 'vite'
import { resolve } from 'path'
import copy from 'rollup-plugin-copy'

export default defineConfig({
  build: {
    lib: {
      entry: [
        resolve(__dirname, 'src/app.ts'),
        resolve(__dirname, 'src/config.ts'),
      ],
      formats: ['cjs']
    },
    rollupOptions: {
      external: [], // 设置为空数组，这样所有依赖都会被打包
      plugins: [
        copy({
          targets: [
            { src: 'src/assets/*', dest: 'dist/assets' }
          ],
          hook: 'writeBundle'
        })
      ]
    },
    sourcemap: false,
  }
})
