import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";
import COS from 'cos-js-sdk-v5';

export interface IConfig {
  region: string;
  accessKeyId: string;
  accessKeySecret: string;
  bucket: string;
  filePath: string;
  domain: string;
  style: string;
}

export interface IExtra {
  key: string;
  location: string;
}




/**
 * 导出插件
 */
export default class CatBoxStoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }

  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    return new Promise((resolve, reject) => {
      const config = this.readConfig(params.storageId);
      const client = this.getClient(config);
      const key = this.formatUploadPath(config.filePath, params);
      client.uploadFile({
        Bucket: config.bucket, // 填入您自己的存储桶，必须字段
        Region: config.region,  // 存储桶所在地域，例如ap-beijing，必须字段
        Key: key,  // 存储在桶里的对象键（例如1.jpg，a/b/test.txt），必须字段
        Body: params.file, // 必须，上传文件对象，可以是input[type="file"]标签选择本地文件后得到的file对象
        SliceSize: 1024 * 1024 * 10,     // 触发分块上传的阈值，超过5MB使用分块上传，非必须
        onTaskReady: function(taskId) {  // 非必须
          // console.log(taskId);
        },
        onProgress: function (progressData) { // 非必须
          // console.log(JSON.stringify(progressData));
        },
        // 支持自定义headers 非必须
        // Headers: {
        //   'x-cos-meta-test': 123
        // },
      }, function(err, data) {
        if (err) {
          throw Error(err.message);
        } else {

          let url = 'https://' + data.Location;

          if (config.domain) {
            const re = new RegExp('^(https|http):\\/\\/(.*?)\\/');
            url = url.replace(re, `${config.domain}/`);
          }

          if (config.style && url.search('imageMogr2') === -1) {
            url = `${url}?imageMogr2/${config.style}`;
          }
          console.log(url);
          resolve({
            url,
            extra: {
              key,
              location: data.Location,
            }
          })
        }
      });
    });
  }


  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        if (!params.extra) {
          throw new Error('缺少必要参数');
        }
        const config = this.readConfig(params.storageId);
        const client = this.getClient(config);
        client.deleteObject({
          Bucket: config.bucket, // 填入您自己的存储桶，必须字段
          Region: config.region,  // 存储桶所在地域，例如ap-beijing，必须字
          Key: params.extra.key
        }, function(err, data) {
          if (err) {
            throw new Error(err.message);
          } else {
            resolve(true);
          }
        });
      }catch (e) {
        reject(e);
      }
    });
  }

  verifyConfig(config: IConfig): Promise<void> {
    const cos = new COS({
      SecretId: config.accessKeyId,
      SecretKey: config.accessKeySecret,
    });
    return new Promise((resolve, reject) => {
      try {
        cos.getBucket({
          Bucket: config.bucket, // 填入您自己的存储桶，必须字段
          Region: config.region,  // 存储桶所在地域，例如 ap-beijing，必须字段
        }, function(err, data) {
          if (data) {
            resolve();
            return;
          }
          if (err) {
            reject(new Error(err.message));
          } else {
            reject(new Error("配置存在问题"));
          }
        });
      }catch (e) {
        reject(new Error("配置存在问题"));
      }
    })
  }

  private getClient(config: IConfig) {
    return new COS({
      SecretId: config.accessKeyId,
      SecretKey: config.accessKeySecret,
    });
  }
}
