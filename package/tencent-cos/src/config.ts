import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "tencent-cos",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "腾讯云 COS",
    pluginDesc: "是由腾讯云推出的无目录层次结构、无数据格式限制，可容纳海量数据且支持 HTTP/HTTPS 协议访问的分布式存储服务",
    pluginVersion: "1.0.4",
    pluginGroup: 'cloudVendor'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: '地域',
          field:  'region',
          rules: {
            required: true,
            message: '地域不能为空',
          }
        },
        elementProperty: {
          placeholder: '地域',
        },
      },
      {
        type: 'input',
        formItem: {
          label: '访问 id',
          field: 'accessKeyId',
          rules: {
            required: true,
            message: '访问 id 必填',
          }
        },
        elementProperty: {
          placeholder: 'accessKeyId'
        }
      },
      {
        type: 'input',
        formItem: {
          label: '访问密匙',
          field:  'accessKeySecret',
          rules: {
            required: true,
            message: '访问密匙不能为空',
          }
        },
        elementProperty: {
          placeholder: 'accessKeySecret',
          type: 'password'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '桶名称',
          field: 'bucket',
          rules: {
            required: true,
            message: 'Bucket 名称',
          }
        },
        elementProperty: {
          placeholder: 'Bucket',
        },
      },
      {
        type: 'input-format',
        formItem: {
          label: '文件路径',
          field: 'filePath',
          rules: [
            {
              required: true,
              message: '请输入文件路径',
            }
          ]
        },
        elementProperty: {
          placeholder: '请输入文件路径',
          defaultValue: '{filename}.{suffix}'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '自定义域名',
          field: 'domain',
          rules: [
            {
              validator: (value: any | undefined, callback: (error?: string) => void) => {
                if (!value) {
                  return;
                }
                if (value.toString().startsWith('http://') || value.toString().startsWith('https://')) {
                  return;
                }
                callback('自定义域名需要 http 和 https 开头');
              }
            }
          ]
        },
        elementProperty: {
        },
      },
      {
        type: 'input',
        formItem: {
          label: '图片样式',
          field: 'style',
        },
        elementProperty: {
          placeholder: '例如：thumbnail/!50p，需要域名支持数据万象 [可选]',
        },
      },
    ]
  }
});
