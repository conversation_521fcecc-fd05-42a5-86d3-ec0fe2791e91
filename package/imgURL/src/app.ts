import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";

const API_ENDPOINT = 'https://www.imgurl.org/api/v2/upload'


export interface IConfig {
  uid: string;
  token: string;
  uploadApi: string;
  fileNameTemplate?: string;
}

export interface IExtra {
  imgId: string;
  deleteUrl: string;
}


interface ImgURLRequest<T = any> {
  code: number;
  msg: string;
  data?: T;
}

interface ImageInfo {
  relative_path: string;
  url: string;
  thumbnail_url: string;
  image_width: number;
  image_height: number;
  client_name: string;
  id: number;
  imgid: string;
  delete: string;
}



/**
 * 导出插件
 */
export default class CatBoxStoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }

  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    const config = this.readConfig(params.storageId);
    if (!config.token) {
      throw new Error('未填写 token 值');
    }
    const formData = new FormData();
    formData.append('uid', config.uid);
    formData.append('token', config.token);
    let fileName = params.allFileName;
    if (config.fileNameTemplate) {
      fileName = this.formatUploadPath(config.fileNameTemplate, params);
    }
    formData.append('file', new File([params.file], fileName));

    const res: ImgURLRequest<ImageInfo> = await fetch(config.uploadApi || API_ENDPOINT, {
      method: 'POST',
      body: formData
    }).then(res => res.json());

    if (res.code != 200 || !res.data) {
      throw new Error(res.msg);
    }

    return  {
      thumbnailUrl: res.data.thumbnail_url,
      url: res.data.url,
      extra: {
        imgId: res.data.imgid,
        deleteUrl: res.data.delete,
      }
    }
  }

  /**
   * 删除图片
   * @param params
   */
  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    const config = this.readConfig(params.storageId);
    if (!params.extra) {
      throw new Error('缺少必要信息');
    }

    await fetch(params.extra.deleteUrl);
    return true;
  }

  async verifyConfig(config: IConfig): Promise<void> {
    const formData = new FormData();
    formData.append('uid', config.uid);
    formData.append('token', config.token);
    const res: ImgURLRequest = await fetch((config.uploadApi || API_ENDPOINT).replace('/api/v2/upload', '/api/v2/albums'), {
      method: 'POST',
      body: formData
    }).then(res => res.json());
    if (res.code !== 200) {
      throw new Error(res.msg);
    }
  }
}
