import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "imgURL",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "ImgURL",
    pluginDesc: "一款图床服务，依然保持以往简洁风格",
    pluginVersion: "1.0.3",
    pluginGroup: 'other'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: 'API URL',
          field:  'uploadApi',
        },
        elementProperty: {
          placeholder: 'API URL 默认 www.imgurl.org',
          defaultValue: 'https://www.imgurl.org/api/v2/upload'
        },
      },
      {
        type: 'input',
        formItem: {
          label: 'uid',
          field:  'uid',
          rules: [
            {
              required: true,
              message: 'uid 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'uid',
        },
      },
      {
        type: 'input',
        formItem: {
          label: 'token',
          field:  'token',
          rules: [
            {
              required: true,
              message: 'token 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'token',
          type: 'password'
        },
      },
      {
        type: 'input-format',
        formItem: {
          type: 'file',
          label: '文件名',
          field: 'fileNameTemplate',
          rules: {
            validator: (value: any | undefined, callback: (error?: string) => void) => {
              if (!value) {
                return;
              }
              if (!value.toString().includes('/') && !value.toString().includes('\\') ) {
                return;
              }
              callback('文件名称不支持使用目录分割符');
            }
          }
        },
        elementProperty: {
          placeholder: '请输入文件名称',
          defaultValue: '{filename}.{suffix}'
        },
      },
    ]
  }
});
