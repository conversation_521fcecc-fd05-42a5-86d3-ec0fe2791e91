import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "chevereto.v4",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "Chevereto V4",
    pluginDesc: "[开源]Chevereto是目前最好的图床之一了。功能也非常强大",
    pluginVersion: "1.0.6",
    pluginGroup: 'self'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: 'API URL',
          field:  'uploadApi',
        },
        elementProperty: {
          placeholder: 'API URL 默认  https://www.imagehub.cc/api/1/upload',
          defaultValue: 'https://www.imagehub.cc/api/1/upload'
        },
      },
      {
        type: 'input',
        formItem: {
          label: 'API key',
          field:  'appKey',
          rules: [
            {
              required: true,
              message: 'API key 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'API key',
          type: 'password'
        },
      },
      {
        type: 'input-format',
        formItem: {
          type: 'file',
          label: '标题',
          field: 'titleTemplate',
          rules: [
            {
              validator: (value: any | undefined, callback: (error?: string) => void) => {
                if (!value) {
                  return;
                }
                if (!value.toString().includes('/') && !value.toString().includes('\\') ) {
                  return;
                }
                callback('标题不支持使用目录分割符');
              }
            }
          ]
        },
        elementProperty: {
          placeholder: '请输入标题名称',
          defaultValue: '{filename}.{suffix}'
        },
      },
    ]
  }
});
