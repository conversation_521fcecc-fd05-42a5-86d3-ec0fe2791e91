import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";

const API_ENDPOINT = 'https://www.imagehub.cc/api/1/upload'
const DEFAULT_FILE_PATH_FORMAT = "{filename}.{suffix}";

export interface IConfig {
  appKey: string;
  uploadApi: string;
  titleTemplate?: string;
}

export interface IExtra {
  md5: string;
  deleteUrl: string;
  key: string;
}


interface UploadRequest<T = any> {
  status_code: number;
  error?: {
    message: string;
  },
  success: {
    message: string;
    code: string;
  }
  status_txt: string;
  image: T;
}

interface FileResponse {
  name: string;
  extension: string;
  size: number;
  width: number;
  height: number;
  date: string;
  date_gmt: string;
  title: string;
  tags: string[];
  description: string | null;
  nsfw: number;
  storage_mode: string;
  md5: string;
  source_md5: string | null;
  original_filename: string;
  original_exifdata: string | null;
  views: number;
  category_id: number | null;
  chain: number;
  thumb_size: number;
  medium_size: number;
  frame_size: number;
  expiration_date_gmt: string;
  likes: number;
  is_animated: number;
  is_approved: number;
  is_360: number;
  duration: number;
  type: string;
  tags_string: string;
  file: {
    resource: {
      type: string;
    };
  };
  id_encoded: string;
  filename: string;
  mime: string;
  url: string;
  ratio: number;
  size_formatted: string;
  frame: {
    filename: string;
    name: string;
    mime: string;
    extension: string;
    url: string;
    size: number;
  };
  image: {
    filename: string;
    name: string;
    mime: string;
    extension: string;
    url: string;
    size: number;
  };
  thumb: {
    filename: string;
    name: string;
    mime: string;
    extension: string;
    url: string;
    size: number;
  };
  url_frame: string;
  medium: {
    filename: string | null;
    name: string | null;
    mime: string | null;
    extension: string | null;
    url: string | null;
  };
  duration_time: string;
  url_viewer: string;
  path_viewer: string;
  url_short: string;
  display_url: string;
  display_width: number;
  display_height: number;
  views_label: string;
  likes_label: string;
  how_long_ago: string;
  date_fixed_peer: string;
  title_truncated: string;
  title_truncated_html: string;
  is_use_loader: boolean;
  display_title: string;
  delete_url: string;
}




/**
 * 导出插件
 */
export default class CheveretoStoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }

  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    const config = this.readConfig(params.storageId);
    if (!config.appKey) {
      throw new Error('未填写 API key 值');
    }
    const formData = new FormData();
    const fileName = this.formatUploadPath(config.titleTemplate || DEFAULT_FILE_PATH_FORMAT, params);
    formData.append('title', fileName);
    formData.append('source', new File([params.file], fileName));
    formData.append('key', config.appKey);
    const res: UploadRequest<FileResponse> = await fetch(config.uploadApi || API_ENDPOINT, {
      headers: {
        'X-API-Key': config.appKey,
      },
      method: 'POST',
      body: formData
    }).then(res => res.json());

    console.log(res);
    if (res.status_code != 200 || !res.image) {
      if (res.error) {
        throw new Error(res.error.message);
      } else {
        throw new Error('上传失败');
      }
    }

    return  {
      thumbnailUrl: res.image.thumb.url,
      url: res.image.url,
      extra: {
        key: this.extractImagePath(res.image.url),
        md5: res.image.md5,
        deleteUrl: res.image.delete_url,
      }
    }
  }

  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    if (!params.extra) {
      return false;
    }
    await fetch(params.extra.deleteUrl)
    return true;
  }
}
