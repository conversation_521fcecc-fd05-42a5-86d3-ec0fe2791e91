import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "github",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "Github",
    pluginDesc: "github 全球最大代码托管平台",
    pluginVersion: "1.0.4",
    pluginGroup: 'other'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: 'token',
          field:  'token',
          rules: [
            {
              required: true,
              message: 'token 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'token',
          type: 'password'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '仓库名',
          field:  'repository',
          rules: [
            {
              required: true,
              message: '仓库名 不能为空',
            }
          ]
        },
        elementProperty: {
          tooltip: '仓库至少要存在一个文件例:「xiaou/picture」',
          placeholder: 'xiaou/picture'
        },
      },
      {
        type: 'input-format',
        formItem: {
          label: '文件路径',
          field: 'filePath',
          rules: [
            {
              required: true,
              message: '请输入文件路径',
            }
          ]
        },
        elementProperty: {
          placeholder: '请输入文件路径',
          defaultValue: '{YY}-{M}/{filename}_{timestamp}.{suffix}'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '分支',
          field: 'branch',
          rules: {
            message: '请输入分支名称',
          }
        },
        elementProperty: {
          placeholder: '[默认使用 master 分支]',
          defaultValue: 'master'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '格式化链接',
          field: 'urlTemplate',
        },
        elementProperty: {
          placeholder: '[可选]默认小于20Mb文件使用 jsDelivr cdn',
        },
      },
    ]
  }
});
