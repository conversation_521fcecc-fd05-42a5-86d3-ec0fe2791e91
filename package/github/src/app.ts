import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";
import CryptoJS from 'crypto-js';

const DEFAULT_FILE_PATH_FORMAT = "{YY}-{M}/{filename}_{timestamp}.{suffix}";
export interface IConfig {
  token: string;
  repository: string;
  filePath: string;
  branch?: string;
  urlTemplate?: string;
}

export interface IExtra {
  sha: string;
  key: string;
  originalUrl: string;
}


interface UploadRequest<T = any> {

}

interface FileResponse {

}




/**
 * 导出插件
 */
export default class CatBoxStoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }

  protected formatStr (formatPath: string, params: Record<string, string> = {}): string {
    for (const key in params) {
      if (params[key]) {
        formatPath = formatPath.replace(new RegExp('\\{' + key + '\\}', 'g'), params[key])
      }
    }
    return formatPath
  }

  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    const config = this.readConfig(params.storageId);

    // 生成文件路径
    const uploadPath = this.formatUploadPath(config.filePath || DEFAULT_FILE_PATH_FORMAT, params);

    // 将文件内容转换为 Base64
    const fileContent = await params.file.arrayBuffer();
    const wordArray = CryptoJS.lib.WordArray.create(fileContent);
    const content = CryptoJS.enc.Base64.stringify(wordArray);

    // 调用 GitHub API 创建文件
    const url = `https://api.github.com/repos/${config.repository}/contents/${uploadPath}`;
    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Authorization': `token ${config.token}`,
        'Accept': 'application/vnd.github.v3+json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: `图床 Plus`,
        content: content,
        branch: config.branch || 'master'
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '上传失败:' + error.status);
    }

    const result = await response.json();
    let fileUrl = result.content.download_url;

    const fillParams: Record<string, string> = {
      branch: config.branch || 'master',
      uploadPath: result.content.path,
      project: config.repository
    }
    if (config.urlTemplate) {
      fileUrl = this.formatStr(config.urlTemplate, fillParams);
    } else if (result.content.size < 1024 * 1024 * 20) {
      const urlTemplate = 'https://fastly.jsdelivr.net/gh/{project}@{branch}/{uploadPath}';
      fileUrl = this.formatStr(urlTemplate, fillParams);
    }
    // 生成返回的 URL

    // if ()
    // if (config.urlTemplate) {
    //   fileUrl = config.urlTemplate.replace('{path}', filePath);
    // }

    return {
      url: fileUrl,
      extra: {
        originalUrl: result.content.download_url,
        sha: result.content.sha,
        key: result.content.path,
      }
    };
  }

  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    const config = this.readConfig(params.storageId);
    if (!params.extra) {
      throw new Error('缺少必要参数');
    }

    // 调用 GitHub API 删除文件
    const url = `https://api.github.com/repos/${config.repository}/contents/${params.extra.key}`;
    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Authorization': `token ${config.token}`,
        'Accept': 'application/vnd.github.v3+json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: `Delete ${params.extra.key}`,
        sha: params.extra.sha,
        branch: config.branch || 'master'
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '删除失败');
    }

    return true;
  }

  async verifyConfig(config: IConfig): Promise<void> {
    // 验证仓库是否存在及权限
    const url = `https://api.github.com/repos/${config.repository}`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `token ${config.token}`,
        'Accept': 'application/vnd.github.v3+json'
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('仓库不存在');
      }
      if (response.status === 401) {
        throw new Error('token 无效或已过期');
      }
      throw new Error(`GitHub API 请求失败: ${response.statusText}`);
    }

    // 验证分支是否存在（使用默认 master 分支或用户指定的分支）
    const branchName = config.branch || 'master';
    const branchUrl = `https://api.github.com/repos/${config.repository}/branches/${branchName}`;
    const branchResponse = await fetch(branchUrl, {
      headers: {
        'Authorization': `token ${config.token}`,
        'Accept': 'application/vnd.github.v3+json'
      }
    });

    if (!branchResponse.ok) {
      if (branchResponse.status === 404) {
        throw new Error(`分支 '${branchName}' 不存在`);
      }
      throw new Error(`验证分支失败: ${branchResponse.statusText}`);
    }
  }
}
