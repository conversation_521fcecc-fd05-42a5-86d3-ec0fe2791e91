{"name": "github", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "vite build", "build:dev": "vite build -c vite.dev.config.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "private": true, "devDependencies": {"@types/node": "^20.12.5", "rollup-plugin-copy": "^3.5.0", "ts-node": "^10.9.2", "typescript": "^5.7.3", "vite": "^6.0.11", "@types/crypto-js": "^4.2.2"}, "dependencies": {"@xiaou66/picture-plugin": "^0.0.36", "crypto-js": "^4.2.0"}}