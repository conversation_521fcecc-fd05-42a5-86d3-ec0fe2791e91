import {IDeleteFileParams, IUploadFileParams, IUploadFileResult, StoragePlugIn} from "@xiaou66/picture-plugin";
import {getContentType, getTemporaryCredentials, TmpTokenData} from "./utils";

import S3 from "aws-sdk/clients/s3";


const DEFAULT_FILE_PATH_FORMAT = "{YY}-{M}/{filename}_{timestamp}.{suffix}";
export interface IConfig {
  region: string;
  bucket: string;
  accessKey: string;
  secretKey: string;
  endpoint: string;
  filePath?: string;
  domain?: string;
  pathStyle?: string;
}

export interface IExtra {
  key: string;
  eTag: string;
}

/**
 * 导出插件
 */
export default class S3StoragePlugIn extends StoragePlugIn<IConfig, IExtra> {
  private __tempMap: Map<string, TmpTokenData> = new Map<string, TmpTokenData>()
  protected doInit() {
  }

  protected doDestroy() {
  }

  uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    return new Promise( async (resolve, reject) => {
      const config = this.readConfig(params.storageId);
      const key = this.formatUploadPath(config.filePath || DEFAULT_FILE_PATH_FORMAT, params);
      const client = await this.getClient(config);
      client.upload({
        Bucket: client.config.params?.Bucket,
        Key: key,
        Body: params.file,
        ContentType: getContentType(params.suffix),
      }, {
        partSize: 10 * 1024 * 1024,
        queueSize: 4
      }, function(err, data) {
        // 如果 s3.upload 传递了第三个参数，表示直接 send
        if (err) {
          reject(err.message);
        } else {
          let url = data.Location;
          if (config.domain) {
            url = config.domain;
            if (!url.endsWith("/")) {
              url += '/';
            }
            url += data.Key;
          }
          resolve({
            url,
            extra: {
              key: data.Key,
              eTag: data.ETag,
            }
          });
        }
      });
    })
  }

  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    if (!params.extra) {
      throw new Error('缺少必要参数');
    }
    const config = this.readConfig(params.storageId);
    const client = await this.getClient(config);

    return new Promise((resolve, reject) => {
      if (!params.extra) {
        reject('缺少必要参数');
        return;
      }
      client.deleteObject({
        Bucket: client.config.params?.Bucket,
        Key: params.extra.key // 需要符合 keyPrefix
      }, function(err, data) {
        if (err) {
          reject(err.message);
        } else {
          resolve(true);
        }
      });
    });
  }

  async verifyConfig(config: IConfig): Promise<void> {
    const tmpTokenData = await getTemporaryCredentials(config.accessKey, config.secretKey);
    const bucketInfoList = tmpTokenData.Buckets
      .filter(({ name }) =>  config.bucket === name);
    if (bucketInfoList.length === 0) {
      throw new Error("bucket 未找到");
    }
  }

  private async getClient(config: IConfig) {
    let tmpTokenData: TmpTokenData | undefined;
    if (this.__tempMap.has(config.accessKey)) {
      const data = this.__tempMap.get(config.accessKey);
      if (data) {
        console.log('data.ExpiredAt - Date.now()', data.ExpiredAt, Date.now(), data.ExpiredAt - (Date.now() / 1000))
        if (data.ExpiredAt - (Date.now() / 1000) > 60 * 10) {
          tmpTokenData = data;
        }
      }
    }

    if (!tmpTokenData) {
      tmpTokenData = await getTemporaryCredentials(config.accessKey, config.secretKey);
      this.__tempMap.set(config.accessKey, tmpTokenData);
    }

    const bucketInfoList = tmpTokenData.Buckets
      .filter(({ name }) =>  config.bucket === name);
    if (bucketInfoList.length === 0) {
      throw new Error("bucket 未找到");
    }

    const bucketInfo = bucketInfoList[0];
    console.log(bucketInfo);
    console.log(bucketInfo.s3Bucket)
    return new S3({ // 用服务端返回的信息初始化一个 S3 实例
      region: 'automatic',
      endpoint: bucketInfo.s3Endpoint,
      credentials: tmpTokenData.Credentials,
      params: {
        Bucket: bucketInfo.s3Bucket
      }
    });
  }
}
