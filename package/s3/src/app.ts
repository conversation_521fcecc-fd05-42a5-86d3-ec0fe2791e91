import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams, UploadFileOptions} from "@xiaou66/picture-plugin";
import {PutObjectCommand, DeleteObjectCommand, S3Client, CreateMultipartUploadCommand, Upload<PERSON>artCommand, CompleteMultipartUploadCommand, AbortMultipartUploadCommand} from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import {getContentType} from "./utils";

const fs = require('fs');

const DEFAULT_FILE_PATH_FORMAT = "{YY}-{M}/{filename}_{timestamp}.{suffix}";
export interface IConfig {
  region: string;
  bucket: string;
  accessKeyId: string;
  secretAccessKey: string;
  endpoint: string;
  filePath?: string;
  domain?: string;
  pathStyle?: string;
}

export interface IExtra {
  key: string;
}

/**
 * 导出插件
 */
export default class S3StoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }


  async uploadFile(params: IUploadFileParams, options: UploadFileOptions = {}): Promise<IUploadFileResult<IExtra>> {
    console.log('fs', fs)
    const config = this.readConfig(params.storageId);
    const key = this.formatUploadPath(config.filePath || DEFAULT_FILE_PATH_FORMAT, params);
    const client = this.getClient(config);
    if (params.file) {
      // 使用 Upload 类进行上传
      const upload = new Upload({
        client: client,
        params: {
          Bucket: config.bucket,
          Key: key,
          Body: params.file,
          ContentType: getContentType(params.suffix),
        },
        // 设置分片大小和并发数
        partSize: 1024 * 1024 * 5, // 5MB per part
        queueSize: 4, // 4 concurrent parts
      });
      // 监听上传进度（可选）
      upload.on("httpUploadProgress", (progress) => {
        if (progress.loaded && progress.total) {
          options.onProgress?.(Math.round((progress.loaded / progress.total) * 100));
        }
      });
      await upload.done();
    } else {
      // 流式上传
      if (!params.filePath) {
        throw new Error('缺少文件或文件路径');
      }
      await this.uploadFileInChunks(params, config, key, client, options)
      // // 获取文件信息
      // const fileStats = fs.statSync(params.filePath);
      // // 方案2: 如果上面不行，可以转换为 Web ReadableStream
      // const nodeStream = fs.createReadStream(params.filePath);
      // const webStream = new ReadableStream({
      //   start(controller) {
      //     nodeStream.on('data', (chunk: any) => {
      //       controller.enqueue(new Uint8Array(chunk));
      //     });
      //     nodeStream.on('end', () => {
      //       controller.close();
      //     });
      //     nodeStream.on('error', (err: any) => {
      //       controller.error(err);
      //     });
      //   }
      // });
      // // 使用 Upload 类进行上传
      // const upload = new Upload({
      //   client: client,
      //   params: {
      //     Bucket: config.bucket,
      //     Key: key,
      //     Body: webStream, // 使用 Node.js Readable 流
      //     ContentType: getContentType(params.suffix),
      //     ContentLength: fileStats.size,
      //   },
      //   // 设置分片大小和并发数
      //   partSize: 1024 * 1024 * 5, // 50MB per part
      //   queueSize: 4, // 4 concurrent parts
      // });
      // // 监听上传进度（可选）
      // upload.on("httpUploadProgress", (progress) => {
      //   if (progress.loaded && progress.total) {
      //     console.log(`上传进度: ${Math.round((progress.loaded / progress.total) * 100)}%`);
      //   }
      // });
      // await upload.done();
    }

    const urlPrefix = config.domain || `${config.endpoint}/${config.bucket}`;
    return {
      url: `${urlPrefix}/${key}`,
      extra: {
        key
      }
    }
  }

  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    if (!params.extra) {
      throw new Error('缺少必要参数');
    }
    const config = this.readConfig(params.storageId);
    const client = this.getClient(config);

    const command = new DeleteObjectCommand({
      Bucket: config.bucket,
      Key: params.extra.key
    });

    try {
      await client.send(command);
      return true;
    } catch (error) {
      throw error;
    }
  }

  async verifyConfig(config: IConfig): Promise<void> {
    // 验证必要的配置参数
    try {
      // 尝试创建客户端并发送一个测试请求
      const client = this.getClient(config);
      const command = new PutObjectCommand({
        Bucket: config.bucket,
        Key: '_test_config',
        Body: new Uint8Array(0),
      });
      await client.send(command);

      // 如果测试文件上传成功，立即删除它
      const deleteCommand = new DeleteObjectCommand({
        Bucket: config.bucket,
        Key: '_test_config'
      });
      await client.send(deleteCommand);
    } catch (error) {
      throw error;
    }
  }

  private getClient(config: IConfig) {

    return new S3Client({
      forcePathStyle: config.pathStyle === 'path-style',
      region: config.region || 'us-east-1',
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
      endpoint: config.endpoint
    });
  }

  /**
   * 备用分块上传方法
   * 当流上传失败时使用此方法
   */
  private async uploadFileInChunks(params: IUploadFileParams, config: IConfig, key: string, client: S3Client, options: UploadFileOptions): Promise<IUploadFileResult<IExtra>> {
    const fileStats = fs.statSync(params.filePath!);
    const chunkSize = 1024 * 1024 * 10; // 10MB chunks
    const maxConcurrent = 4; // 最大并发数
    let completedChunks = 0; // 用于跟踪已完成的块数

    // 创建多部分上传
    const createCommand = new CreateMultipartUploadCommand({
      Bucket: config.bucket,
      Key: key,
      ContentType: getContentType(params.suffix),
    });

    const { UploadId } = await client.send(createCommand);

    try {
      const parts: { ETag: string; PartNumber: number }[] = [];
      const totalChunks = Math.ceil(fileStats.size / chunkSize);

      // 创建所有分块的上传任务
      const uploadTasks = Array.from({ length: totalChunks }, (_, i) => {
        return async () => {
          const start = i * chunkSize;
          const end = Math.min(start + chunkSize, fileStats.size);

          // 读取文件块
          const buffer = Buffer.alloc(end - start);
          const fd = fs.openSync(params.filePath!, 'r');
          fs.readSync(fd, buffer, 0, end - start, start);
          fs.closeSync(fd);

          // 上传块
          const uploadCommand = new UploadPartCommand({
            Bucket: config.bucket,
            Key: key,
            PartNumber: i + 1,
            UploadId,
            Body: buffer,
          });

          const result = await client.send(uploadCommand);
          parts[i] = {
            ETag: result.ETag!,
            PartNumber: i + 1,
          };

          // 使用原子操作更新进度
          completedChunks++;
          options.onProgress?.(Math.round((completedChunks / totalChunks) * 100));
        };
      });

      // 并发控制函数
      const concurrentUpload = async (tasks: (() => Promise<void>)[]) => {
        const chunks = [];
        for (let i = 0; i < tasks.length; i += maxConcurrent) {
          const chunk = tasks.slice(i, i + maxConcurrent);
          chunks.push(chunk);
        }

        for (const chunk of chunks) {
          await Promise.all(chunk.map(task => task()));
        }
      };

      // 执行并发上传
      await concurrentUpload(uploadTasks);

      // 完成多部分上传
      const completeCommand = new CompleteMultipartUploadCommand({
        Bucket: config.bucket,
        Key: key,
        UploadId,
        MultipartUpload: { Parts: parts },
      });

      await client.send(completeCommand);

      const urlPrefix = config.domain || `${config.endpoint}/${config.bucket}`;
      return {
        url: `${urlPrefix}/${key}`,
        extra: { key }
      };

    } catch (error) {
      // 如果出错，取消多部分上传
      const abortCommand = new AbortMultipartUploadCommand({
        Bucket: config.bucket,
        Key: key,
        UploadId,
      });
      await client.send(abortCommand);
      throw error;
    }
  }
}
