 const MIME_TYPES: Record<string, string> = {
    // 图片
   'png': 'image/png',
   'jpg': 'image/jpeg',
   'jpeg': 'image/jpeg',
   'gif': 'image/gif',
   'webp': 'image/webp',
   'svg': 'image/svg+xml',
   'ico': 'image/x-icon',
   'bmp': 'image/bmp',
   'tiff': 'image/tiff',
   'avif': 'image/avif ',
   'avifs': 'image/avif ',
   'heic': 'image/heic',
   'heif': 'image/heic',
    'avcs': 'image/avcs',
    'avci': 'image/avci',
   // 文档
   'pdf': 'application/pdf',
   'doc': 'application/msword',
   'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
   'xls': 'application/vnd.ms-excel',
   'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
   'ppt': 'application/vnd.ms-powerpoint',
   'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',

   // 文本
   'txt': 'text/plain',
   'html': 'text/html',
   'htm': 'text/html',
   'css': 'text/css',
   'csv': 'text/csv',
   'xml': 'application/xml',
   'json': 'application/json',
   'js': 'text/javascript',
   'md': 'text/markdown',

   // 音频
   'mp3': 'audio/mpeg',
   'wav': 'audio/wav',
   'ogg': 'audio/ogg',
   'aac': 'audio/aac',
   'm4a': 'audio/mp4',

   // 视频
   'mp4': 'video/mp4',
   'webm': 'video/webm',
   'avi': 'video/x-msvideo',
   'mov': 'video/quicktime',
   'wmv': 'video/x-ms-wmv',

   // 压缩文件
   'zip': 'application/zip',
   'rar': 'application/x-rar-compressed',
   '7z': 'application/x-7z-compressed',
   'gz': 'application/gzip',
   'tar': 'application/x-tar',

   // 字体
   'ttf': 'font/ttf',
   'otf': 'font/otf',
   'woff': 'font/woff',
   'woff2': 'font/woff2',

   // 3D和图形
   'obj': 'model/obj',
   'stl': 'model/stl',
   'gltf': 'model/gltf+json',
   'glb': 'model/gltf-binary',

   // 其他常用格式
   'swf': 'application/x-shockwave-flash',
   'iso': 'application/x-iso9660-image',
   'epub': 'application/epub+zip',
   'apk': 'application/vnd.android.package-archive',
}

 function getContentType(ext: string): string {
   return MIME_TYPES[ext.toLowerCase()] || 'application/octet-stream';
 }
 export {
   getContentType
 }
