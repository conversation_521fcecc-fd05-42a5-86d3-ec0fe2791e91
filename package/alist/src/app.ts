import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";
import api from './api';

const DEFAULT_FILE_PATH_FORMAT = "{YY}-{M}/{filename}_{timestamp}.{suffix}";

const DEFAULT_MOUNT_PATH = '/';



export interface IConfig {
  serviceUrl: string;
  username: string;
  password: string;
  filePath: string;
  mountPath?: string;
}

export interface IExtra {
  filePath?: string;
}


interface FileResponse {
  ok: boolean;
  src: string;
}


/**
 * 导出插件
 */
export default class Image16StoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }

  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    const config = this.readConfig(params.storageId);

    // 获取令牌
    const authData = await api.getToken(config.serviceUrl, config.username, config.password);

    // 构建文件路径
    const fileName = this.formatUploadPath(config.filePath || DEFAULT_FILE_PATH_FORMAT, params);
    // 获取挂载路径，使用默认值如果未设置
    const mountPath = config.mountPath || DEFAULT_MOUNT_PATH;
    // 构建完整的文件路径
    const filePath = mountPath.endsWith('/')
      ? `${mountPath}${fileName}`
      : `${mountPath}/${fileName}`;

    await api.putFormFile(config.serviceUrl, authData, params.file, filePath);
    const fileData = await api.getFileInfo(config.serviceUrl, authData, filePath);
    const urlPrefix = filePath.startsWith('/')
      ? config.serviceUrl + '/d'
      : config.serviceUrl + '/d/';
    let url = `${urlPrefix}${filePath}`;
    if (fileData.sign) {
      url += '?sign=' + fileData.sign;
    }
    return {
      url,
      extra: {
        filePath,
      },
    };
  }

  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    if (!params.extra || !params.extra.filePath) {
      throw Error('缺少必要参数');
    }

    const config = this.readConfig(params.storageId);

    // 获取令牌
    const authData = await api.getToken(config.serviceUrl, config.username, config.password);
    await api.deleteFile(config.serviceUrl, authData, params.extra.filePath);
    return true;
  }

  async verifyConfig(config: IConfig): Promise<void> {
    // 去掉最后 /
    config.serviceUrl.endsWith('/') && (config.serviceUrl = config.serviceUrl.slice(0, -1));
    await api.getToken(config.serviceUrl, config.username, config.password);
    return;
  }
}
