import { SHA256 } from 'crypto-js';


export interface Token {
  token: string;
  expires: number;
}

/**
 * 上传文件到指定路径
 *
 * @param host 服务器地址
 * @param token 认证令牌
 * @param file 待上传的文件
 * @param filePath 文件存储路径
 * @returns 返回一个 Promise，无返回值
 * @throws 如果上传失败，会抛出错误
 */
async function putFormFile(host: string, token: Token, file: File, filePath: string): Promise<void> {
// 准备FormData
  const formData = new FormData();
  formData.append('file', file);
  // 发送上传请求
  const response = await fetch(`${host}/api/fs/form`, {
    method: 'PUT',
    headers: {
      'Authorization': token.token,
      'File-Path': encodeURIComponent(filePath),
    },
    body: formData
  });

  const responseData = await response.json();

  if (responseData.code !== 200) {
    throw new Error(responseData.message || '上传失败');
  }
}

export interface FileData {
  name: string; // 文件名
  size: number; // 大小
  is_dir: boolean; // 是否是文件夹
  modified: string; // 修改时间
  sign: string; // 签名
  thumb: string; // 缩略图
  type: number; // 类型
  raw_url: string; // 原始url
  readme: string; // 说明
  provider: string; // 供应商
  related: any | null; // 相关内容，类型为null或者其他
  created: string; // 创建时间
  hashinfo: string; // Hash信息
  hash_info: any | null; // Hash信息，类型为null或者其他
  header: string; // 头部信息
}

async function getFileInfo(host: string, token: Token, filePath: string): Promise<FileData> {
  const response = await fetch(host + '/api/fs/get', {
    method: 'POST',
    headers: {
      'Authorization': token.token,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      path: filePath
    })
  });

  const data = await response.json();
  if (data.code !== 200) {
    throw new Error(data.message);
  }
  return data.data;
}

const tokenPool: Map<string, Token> = new Map<string, Token>();
/**
 * 获取用户 token
 * @param host
 * @param username 用户名
 * @param password 密码
 * @returns Promise<{token: string}>
 */
async function getToken(host: string, username: string, password: string): Promise<Token> {
  const key = `${host}-${username}`;
  const token = tokenPool.get(key);
  if (token && token.expires > Date.now()) {
    return tokenPool.get(key)!;
  }

  const response = await fetch(host + '/api/auth/login/hash', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      username,
      // 按照文档要求,需要先将密码加上后缀再进行sha256哈希
      password: SHA256(password + '-https://github.com/alist-org/alist').toString()
    })
  });

  const data = await response.json();
  if (data.code !== 200) {
    throw new Error(data.message);
  }

  const newToken = {
    token: data.data.token,
    expires: Date.now() + 24 * 60 * 60 * 1000,
  };

  tokenPool.set(key, newToken);
  return newToken;
}
async function deleteFile(host: string, token: Token, filePath: string): Promise<void> {
  const lastSlashIndex = filePath.lastIndexOf('/');
  const directory = filePath.slice(0, lastSlashIndex);
  const fileName = filePath.slice(lastSlashIndex + 1);
  const response = await fetch(host + '/api/fs/remove', {
    method: 'POST',
    headers: {
      'Authorization': token.token,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      dir: directory,
      names: [fileName],
    })
  });

  const data = await response.json();
  if (data.code !== 200) {
    throw new Error(data.message);
  }
}

export default {
  getToken,
  putFormFile,
  getFileInfo,
  deleteFile
}
