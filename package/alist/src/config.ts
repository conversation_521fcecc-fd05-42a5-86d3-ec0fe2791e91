import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  noConfig: false,
  pluginInfo: {
    pluginCode: "AList",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "AList",
    pluginDesc: " 一个支持多种存储的文件列表程序",
    pluginVersion: "1.0.1",
    pluginGroup: 'self'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: '服务器地址',
          field:  'serviceUrl',
          rules: [
            {
              required: true,
              message: '服务器地址 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: '请输入您的 AList 链接地址'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '用户名',
          field:  'username',
          rules: [
            {
              required: true,
              message: '用户名 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: '请输入您的 AList 用户名'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '密码',
          field:  'password',
          rules: [
            {
              required: true,
              message: '密码 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: '请输入您的 AList 密码',
          type: 'password'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '挂载路径',
          field:  'mountPath',
          tooltip: '挂载路径将优先拼接文件路径前',
        },
        elementProperty: {
          placeholder: '挂载路径默认是 / (根目录)'
        },
      },
      {
        type: 'input-format',
        formItem: {
          label: '文件路径',
          field: 'filePath',
        },
        elementProperty: {
          placeholder: '请输入文件路径',
          defaultValue: '{YY}-{M}/{filename}_{timestamp}.{suffix}'
        },
      },
    ]
  }
});
