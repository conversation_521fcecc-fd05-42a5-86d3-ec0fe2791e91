import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";
const DEFAULT_FILE_PATH_FORMAT = "{filename}.{suffix}";
export interface IConfig {
  domain: string;
  token: string;
  fileNameTemplate?: string;
}

export interface IExtra {
  del: string;
  /**
   * 图片路径
   */
  key: string;
}


interface BaseRequest<T = any> {
  result: string;
  code: number;
  url: string;
  srcName: string;
  thumb: string;
  del: string;
}


const BASE_URL = 'https://v0.api.upyun.com';

/**
 * 导出插件
 */
export default class CatBoxStoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }


  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    const config = this.readConfig(params.storageId);
    const fileName = this.formatUploadPath(config.fileNameTemplate || DEFAULT_FILE_PATH_FORMAT, params);
    const formData = new FormData();
    formData.append('image', new File([params.file], fileName));
    formData.append('token', config.token);
    const res: BaseRequest = await fetch(`${config.domain}`, {
      method: 'POST',
      body: formData,
    }).then(res => res.json());
    if (res.code === 200) {
      return {
        url: res.url,
        extra: {
          del: res.del,
          key: this.extractImagePath(res.url),
        }
      }
    } else {
      throw new Error(res.result);
    }
  }

  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    if (!params.extra) {
      throw new Error('缺少必要参数');
    }
    await fetch(params.extra.del);
    return true;
  }
}
