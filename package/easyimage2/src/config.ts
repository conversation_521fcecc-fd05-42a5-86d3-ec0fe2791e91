import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "easyImage2",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "EasyImage 2.0",
    pluginDesc: "[开源]支持多文件上传,简单无数据库,返回图片url,markdown,bbscode,html的一款图床程序",
    pluginVersion: "1.0.3",
    pluginGroup: 'self'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: 'API调用地址',
          field: 'domain',
          rules: [
            {
              required: true,
              message: '域名不能为空',
            },
            {
              validator: (value: any | undefined, callback: (error?: string) => void) => {
                if (!value) {
                  return;
                }
                if (value.toString().startsWith('http://') || value.toString().startsWith('https://')) {
                  return;
                }
                callback('域名需要 http 和 https 开头');
              }
            }
          ]
        },
        elementProperty: {
          placeholder: 'http://127.0.0.1:20061/api/index.php'
        },
      },
      {
        type: 'input',
        formItem: {
          label: 'token',
          field:  'token',
          rules: {
            required: true,
            message: 'token 不能为空',
          }
        },
        elementProperty: {
          placeholder: 'token',
          type: 'password'
        },
      },
      {
        type: 'input-format',
        formItem: {
          type: 'file',
          label: '文件名',
          field: 'fileNameTemplate',
          rules: {
            validator: (value: any | undefined, callback: (error?: string) => void) => {
              if (!value) {
                return;
              }
              if (!value.toString().includes('/') && !value.toString().includes('\\') ) {
                return;
              }
              callback('文件名称不支持使用目录分割符');
            }
          }
        },
        elementProperty: {
          placeholder: '请输入文件名称',
          defaultValue: '{filename}.{suffix}'
        },
      },
    ]
  }
});
