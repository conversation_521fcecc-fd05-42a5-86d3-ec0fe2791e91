import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "upyun-storage",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "又拍云存储",
    pluginDesc: "[免费额度]是面向非结构化数据的对象存储服务，具有使用简单、高稳定、高安全等特点",
    pluginVersion: "1.0.3",
    pluginGroup: 'cloudVendor'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: '域名',
          field: 'domain',
          rules: [
            {
              required: true,
              message: '域名不能为空',
            },
            {
              validator: (value: any | undefined, callback: (error?: string) => void) => {
                if (!value) {
                  return;
                }
                if (value.toString().startsWith('http://') || value.toString().startsWith('https://')) {
                  return;
                }
                callback('域名需要 http 和 https 开头');
              }
            }
          ]
        },
        elementProperty: {
          placeholder: '域名需要 http 和 https 开头'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '服务名称',
          field:  'serviceName',
          rules: {
            required: true,
            message: '服务名称不能为空',
          }
        },
        elementProperty: {
          placeholder: '服务名称',
        },
      },
      {
        type: 'input',
        formItem: {
          label: '操作员',
          field:  'username',
          rules: {
            required: true,
            message: '操作员不能为空',
          }
        },
        elementProperty: {
          placeholder: '操作员',
        },
      },
      {
        type: 'input',
        formItem: {
          label: '密码',
          field:  'password',
          rules: {
            required: true,
            message: '密码不能为空',
          }
        },
        elementProperty: {
          placeholder: '操作员密码',
          type: 'password'
        },
      },
      {
        type: 'input-format',
        formItem: {
          label: '文件路径',
          field: 'filePath',
        },
        elementProperty: {
          placeholder: '请输入文件路径',
          defaultValue: '{YY}-{M}/{filename}_{timestamp}.{suffix}'
        },
      },
    ]
  }
});
