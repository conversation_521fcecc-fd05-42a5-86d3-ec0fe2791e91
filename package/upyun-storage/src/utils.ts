import CryptoJS from 'crypto-js';

 function sign(operator: string, password: string, method: string, uri: string, date: string): string {
  // 按照又拍云文档要求拼接待签名字符串
  const stringToSign = method + '&' + uri + '&' + date;

  // 使用 HMAC-SHA1 计算签名
  const hmac = CryptoJS.HmacSHA1(stringToSign, password);
  const signature = CryptoJS.enc.Base64.stringify(hmac);

  // 返回最终的 Authorization 头
  return `UPYUN ${operator}:${signature}`;
}

function MD5(value: string): string {
  return CryptoJS.MD5(value).toString();
}

function hmacsha1(secret: string, value: string): string {
  const hmac = CryptoJS.HmacSHA1(value, secret);
  return CryptoJS.enc.Base64.stringify(hmac);
}

function getUTCDate(): string {
  return new Date().toUTCString();
}

function getFormToken(operator: string, password: string,
                      bucketName: string, path: string) {
  const expiration = ((Date.now() / 1000) >>> 0) + 30 * 60
  const method = 'POST'

  // 使用 crypto-js 的 Base64 编码
  const policy = CryptoJS.enc.Base64.stringify(
    CryptoJS.enc.Utf8.parse(
      JSON.stringify({
        bucket: bucketName,
        'save-key': path || '/{filename}{.suffix}',
        expiration: expiration
      })
    )
  )

  // 使用 crypto-js 计算签名
  const authorization = 'UPYUN ' + operator + ':' +
    CryptoJS.enc.Base64.stringify(
      CryptoJS.HmacSHA1(
        method + '&/' + bucketName + '&' + policy,
        CryptoJS.MD5(password).toString()
      )
    )

  return {
    authorization,
    policy
  }
}

export {
  sign,
  MD5,
  getUTCDate,
  getFormToken,
}

// // 使用示例:
// /*
// const key = 'upyun';
// const secret = 'secret';
// const method = 'GET';
// const uri = '/v1/apps/';
// const date = getUTCDate();
//
// // 上传、处理、内容识别(有存储)场景
// console.log(sign(key, MD5(secret), method, uri, date));
//
// // 内容识别(无存储)、容器云场景
// console.log(sign(key, secret, method, uri, date));
// */
