import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";
import {getFormToken, getUTCDate, MD5, sign} from "./utils";
const DEFAULT_FILE_PATH_FORMAT = "{YY}-{M}/{filename}_{timestamp}.{suffix}";
export interface IConfig {
  domain: string;
  serviceName: string;
  username: string;
  password: string;
  filePath?: string;
}

export interface IExtra {
  key: string;
}


interface BaseRequest<T = any> {
  code?: string;
  msg: string;
}

interface FileResponse {
  code?: string;
  msg: string;
}


const BASE_URL = 'https://v0.api.upyun.com';

/**
 * 导出插件
 */
export default class CatBoxStoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }


  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    const config = this.readConfig(params.storageId);
    const key = this.formatUploadPath(config.filePath || DEFAULT_FILE_PATH_FORMAT, params);
    const { policy, authorization } = getFormToken(config.username, config.password, config.serviceName, key);
    const formData = new FormData()
    formData.append('file', params.file)
    formData.append('policy', policy)
    formData.append('authorization', authorization)
    const res = await fetch(`http://v0.api.upyun.com/${config.serviceName}`, {
      method: 'POST',
      body: formData
    }).then(res => res.json());

    if (res.code !== 200) {
      throw new Error(res.message);
    }

    return  {
      url: `${config.domain}/${res.url}`,
      extra: {
        key: key,
      }
    }
  }

  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    if (!params.extra) {
      throw new Error("缺少必要参数");
    }
    const config = this.readConfig(params.storageId);
    const utcDate = getUTCDate();
    const serviceName = config.serviceName;
    const uri = `/${serviceName}/${encodeURI(params.extra.key)}`;

    const authorization = sign(
      config.username,
      MD5(config.password),
      'DELETE',
      uri,
      utcDate
    );

    const response = await fetch(`${BASE_URL}${uri}`, {
      method: 'DELETE',
      headers: {
        'Authorization': authorization,
        'x-date': utcDate,
      }
    });

    // 根据文档，返回 200 表示删除成功
    if (response.status === 200) {
      return true;
    }

    // 删除失败，抛出错误
    const res = await response.json();
    throw new Error(res.msg || '删除文件失败');
  }

  async verifyConfig(config: IConfig): Promise<void> {
    const utcDate = getUTCDate();
    const serviceName = config.serviceName;
    const uri = `/${serviceName}/?usage`;
    // 构建签名认证
    const authorization = sign(
      config.username,
      MD5(config.password), // 密码需要先进行MD5加密
      'GET',
      uri,
      utcDate
    );
    const res: BaseRequest = await fetch(`${BASE_URL}${uri}`, {
      headers: {
        'Authorization': authorization,
        'x-date': utcDate,
        'Accept': 'application/json',
      }
    }).then(res => res.json());
    if (res.code) {
      throw Error(res.msg);
    }
  }
}
