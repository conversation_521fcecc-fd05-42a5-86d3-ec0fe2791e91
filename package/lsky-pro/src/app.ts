import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";
const DEFAULT_FILE_PATH_FORMAT = "{filename}.{suffix}";
export interface IConfig {
  email: string;
  password: string;
  domain: string;
  token: string;
  strategyId?: string;
  fileNameTemplate?: string;
}

export interface IExtra {
  key: string;
  sha1: string;
  md5: string;
}

// {"status":false,"message":"Unauthenticated.","data":{}}
interface BaseRequest<T = any> {
  status: boolean;
  message: string;
  data?: T
}

interface FileResponse {
  /** 图片唯一密钥 */
  key: string;

  /** 图片名称 */
  name: string;

  /** 图片路径名 */
  pathname: string;

  /** 图片原始名 */
  origin_name: string;

  /**
   * 图片大小
   * @description 单位为 KB
   */
  size: number;

  /** 图片 MIME 类型 */
  mimetype: string;

  /** 图片扩展名 */
  extension: string;

  /** 图片 MD5 哈希值 */
  md5: string;

  /** 图片 SHA1 哈希值 */
  sha1: string;

  /** 图片相关链接信息 */
  links: {
    /** 图片访问地址 */
    url: string;

    /** HTML 格式的图片引用代码 */
    html: string;

    /** BBCode 格式的图片引用代码 */
    bbcode: string;

    /** Markdown 格式的图片引用代码 */
    markdown: string;

    /** 带链接的 Markdown 格式图片引用代码 */
    markdown_with_link: string;

    /** 缩略图访问地址 */
    thumbnail_url: string;
  };
}



/**
 * 导出插件
 */
export default class CatBoxStoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }


  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    const config = this.readConfig(params.storageId);
    const formData = new FormData();
    const filename = this.formatUploadPath(config.fileNameTemplate || DEFAULT_FILE_PATH_FORMAT, params);
    formData.append('file', new File([params.file], filename));

    if (config.strategyId) {
      formData.append('strategy_id', config.strategyId);
    }
    const res: BaseRequest<FileResponse> = await fetch(`${config.domain}/upload`, {
      method: 'POST',
      body: formData,
      headers: {
        Authorization: `Bearer ${config.token}`,
        Accept: 'application/json',
      }
    }).then(res => res.json());

    if (!res.status || !res.data) {
      throw new Error(res.message);
    }

    return {
      url: res.data.links.url,
      thumbnailUrl: res.data.links.thumbnail_url,
      extra: {
        key: res.data.key,
        md5: res.data.md5,
        sha1: res.data.sha1
      }
    };

  }

  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    if (!params.extra) {
      throw new Error("缺少必要参数");
    }
    const config = this.readConfig(params.storageId);

    const res: BaseRequest = await fetch(`${config.domain}/images/${params.extra.key}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${config.token}`,
        Accept: 'application/json',
      }
    }).then(res => res.json());

    if (!res.status) {
      throw new Error(res.message);
    }

    return true;
  }

  async verifyConfig(config: IConfig): Promise<void> {
    if ((!config.email || !config.password) && !config.token) {
      throw Error('email 和 password 或 token 必填一组');
    }

    const formData = new FormData();
    formData.append('email', config.email);
    formData.append('password', config.password);
    if (config.email && config.password) {
      const res: BaseRequest = await fetch(`${config.domain}/tokens`, {
        method: 'POST',
        body: formData,
      }).then(res => res.json());
      if (!res.status) {
        throw new Error(res.message);
      }
      config.token = res.data.token
    } else {
      const newVar = await fetch(`${config.domain}/profile`, {
        headers: {
          Authorization: `Bearer ${config.token}`,
        }
      }).then(res => res.json());
      console.log(newVar);
    }

  }
}
