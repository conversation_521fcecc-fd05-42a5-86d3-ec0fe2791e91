import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "LskyPro",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "LskyPro+",
    pluginDesc: "[开源]一个使用 PHP 语言，采用 Laravel 框架开发的一款 Web 图片管理程序，中文名：兰空图床",
    pluginVersion: "1.0.5",
    pluginGroup: 'self'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: '域名',
          field: 'domain',
          tooltip: '可以直接填后台的接口URL即可',
          rules: [
            {
              required: true,
              message: '域名不能为空',
            },
            {
              validator: async (value: any | undefined) => {
                if (!value) {
                  return true;
                }
                if (value.toString().startsWith('http://') || value.toString().startsWith('https://')) {
                  return true;
                }
                return { result: false, message: '域名需要 http 和 https 开头', type: 'warning' };
              }
            }
          ]
        },
        elementProperty: {
          placeholder: 'http://127.0.0.1:8000/api/v1'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '邮箱',
          field:  'email',
        },
        elementProperty: {
          placeholder: '邮箱'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '密码',
          field:  'password',
        },
        elementProperty: {
          type: 'password',
          placeholder: '密码',
        },
      },
      {
        type: 'input',
        formItem: {
          label: 'token',
          field:  'token',
        },
        elementProperty: {
          placeholder: '不填入将使用用户名和密码自动获取',
          type: 'password',
          // readonly: true
        },
      },
      {
        type: 'input',
        formItem: {
          label: '策略 id',
          field:  'strategyId',
        },
        elementProperty: {
          placeholder: 'strategy_id',
        },
      },
      {
        type: 'input-format',
        formItem: {
          type: 'file',
          label: '文件名',
          field: 'fileNameTemplate',
          rules: {
            validator: (value: any | undefined, callback: (error?: string) => void) => {
              if (!value) {
                return;
              }
              if (!value.toString().includes('/') && !value.toString().includes('\\') ) {
                return;
              }
              callback('文件名称不支持使用目录分割符');
            }
          }
        },
        elementProperty: {
          placeholder: '请输入文件名称',
          defaultValue: '{filename}.{suffix}'
        },
      },
    ]
  }
});
