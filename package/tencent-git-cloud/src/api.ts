
const DEFAULT_BASE_URL = 'https://api.cnb.cool';

/**
 * 获取分支列表
 * @param organization 组织名
 * @param repository 仓库名
 * @param token 访问令牌
 * @param page 页码
 * @param pageSize 每页数量
 */
async function getBranches(token: string, organization: string, repository: string,
                           page: number = 1, pageSize: number = 30): Promise<any> {
  const response = await fetch(`${DEFAULT_BASE_URL}/${organization}/${repository}/-/git/branches?page=${page}&page_size=${pageSize}`, {
    method: 'GET',
    headers: {
      'accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    }
  }).then(res => res.json());

  if (response.errcode) {
    throw new Error(response.errmsg);
  }

  return response;
}

async function getBranchInfo(token: string, organization: string, repository: string, branch: string): Promise<any> {
  const response = await fetch(`${DEFAULT_BASE_URL}/${organization}/${repository}/-/git/branches/${branch}`, {
    method: 'GET',
    headers: {
      'accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    }
  });

  if (response.status !== 200) {
    throw new Error('获取分支信息失败可能是分支不存在');
  }
  const res = await response.json();

  if (res.errcode) {
    throw new Error(res.errmsg);
  }

  return res;
}

/**
 * 创建一个 blob
 * @param token
 * @param organization
 * @param repository
 * @param branch
 */
async function getCreateBlob(token: string, organization: string, repository: string, branch: string, ): Promise<any> {
  const response = await fetch(`${DEFAULT_BASE_URL}/${organization}/${repository}/-/git/branches/${branch}/protected`, {
    method: 'GET',
    headers: {
      'accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    }
  });

  if (response.status !== 200) {
    throw new Error('获取分支保护信息失败可能是分支不存在');
  }
  const res = await response.json();

  if (res.errcode) {
    throw new Error(res.errmsg);
  }

  return res;
}

export default {
  getBranches,
  getBranchInfo
}
