import {StoragePlugIn, IUploadFileParams, IUploadFileResult, IDeleteFileParams} from "@xiaou66/picture-plugin";
import api from "./api";
export interface IConfig {
  token: string;
  organization: string;
  repository: string;
  branch: string;
  fileNameTemplate?: string;
}

export interface IExtra {
  hash: string;
  path: string;
}


/**
 * 导出插件
 */
export default class CatBoxStoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {

  }

  protected doDestroy() {
  }



  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    
      return {} as any;
    }

  /**
   * 删除图片
   * @param params
   */
  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    return true;
  }

  async verifyConfig(config: IConfig): Promise<void> {
    if (!config.branch) {
      const res = await api.getBranches(config.token, config.organization, config.repository);
      if (res.length === 0) {
        throw new Error('请先创建一个分支');
      }
      config.branch = res[0].name;
    } else {
      await api.getBranchInfo(config.token, config.organization, config.repository, config.branch);
    }
  }
}
