import {createStoragePlugInConfig} from "@xiaou66/picture-plugin";

export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "onedrive",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "OneDrive",
    pluginDesc: "OneDrive 是一个强大但易于使用的云存储平台，适用于小型企业、企业以及介于两者之间的一切。",
    pluginVersion: "1.0.3",
    pluginGroup: 'cloudVendor'
  },
  uiConfig: {
    tips: `<div>1. 本存储源远程删除是将文件移动回收站而不是永久删除</div>
           <div>2. 如果出现无法上传请重新获取令牌</div>
          `,
    forms: [
      {
        type: 'input-button',
        formItem: {
          label: '令牌',
          field: 'token',
          buttonText: '获取令牌',
          rules: [
            {
              required: true,
              message: '请授权给图床 Plus 让其获取 token',
            },
          ],
          click: async (config: any) => {
            const res = await utools.ubrowser.goto('https://cloud.on-u.cn/file-plus/one-drive/auth')
              .wait('pre', 10 * 60 * 1000)
              .evaluate(() => {
                // @ts-ignore
                return JSON.parse(document.querySelector('pre').textContent)
              })
              .clearCookies()
              .hide()
              .run({});
            utools.clearUBrowserCache();
            if (res && res.length) {
              const {code, message, data} = res[0]
              if (code !== 0) {
                throw new Error(message);
              }
              if (!data.refresh_token) {
                throw new Error('授权失败');
              }

              config.value.token = data.access_token;
              config.value.refreshToken = data.refresh_token;
            } else {
              throw new Error('授权失败');
            }
          }
        },
        elementProperty: {},
      },
      {
        type: 'input-format',
        formItem: {
          label: '文件路径',
          field: 'filePath',
          rules: {
            required: true,
            message: '请输入文件路径',
          }
        },
        elementProperty: {
          defaultValue: '{YY}-{M}/{filename}_{timestamp}.{suffix}'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '直链解析服务',
          field: 'serviceUrl',
        },
        elementProperty: {
          placeholder: '[默认使用作者提供的服务] 作者提供服务限时免费',
        },
      },
    ]
  }
});
