import {IDeleteFileParams, IUploadFileParams, IUploadFileResult, StoragePlugIn} from "@xiaou66/picture-plugin";

const DEFAULT_FILE_PATH_FORMAT = "{YY}-{M}/{filename}_{timestamp}.{suffix}";
const DEFAULT_SERVICE_URL = "https://cloud.on-u.cn/file-plus/one-drive";
// const DEFAULT_SERVICE_URL = "http://localhost:8888/file-plus/one-drive";
export interface IConfig {
  /**
   * token
   */
  token: string;
  /**
   * 刷新 token
   */
  refreshToken: string;
  /**
   * 文件路径
   */
  filePath?: string;
  /**
   * 服务地址
   */
  serviceUrl?: string;
}

export interface IExtra {
  id: string;
  path: string;
  driveId: string;
  shareId: string;
}

// {"status":false,"message":"Unauthenticated.","data":{}}
interface BaseRequest<T = any> {
  status: boolean;
  message: string;
  data?: T
}

interface FileResponse {
  id: string;
  cTag: string;
  name: string;
  parentReference: {
    path: string;
    name: string;
    driveId: string;
    id: string;
  }
}

interface FileShareResponse {
  hasPassword: boolean;
  id: string;
  link: {
    preventsDownload: boolean;
    scope: string;
    type: string;
    webUrl: string;
  };
  roles: string[];
  shareId: string;
}



/**
 * 导出插件
 */
export default class CatBoxStoragePlugIn extends StoragePlugIn<IConfig, IExtra> {

  protected doInit() {
  }

  protected doDestroy() {
  }



  async uploadSmall(storageId: string,file: File, path: string, config: IConfig) {
    return fetch(`https://graph.microsoft.com/v1.0/me/drive/root:/${path}:/content`, {
      method: 'PUT',
      // @ts-ignore
      body: file,
      headers: {
        Authorization: `Bearer ${await this.getToken(storageId, config)}`
      }
    }).then(res => res.json());
  }

  async createShareLink(id: string, storageId: string, config: IConfig) {
    return  await fetch(`https://graph.microsoft.com/v1.0/me/drive/items/${id}/createLink`, {
      method: 'POST',
      body: JSON.stringify({
        type: 'view',
        retainInheritedPermissions: false,
        scope: 'anonymous'
      }),
      headers: {
        Authorization: `Bearer ${await this.getToken(storageId, config)}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.json());
  }

  // 分块上传函数（支持断点续传）
  async uploadChunk(
    uploadUrl: string,
    chunk: Blob,
    contentRange: string,
    token: string
  ): Promise<Response> {
    return fetch(uploadUrl, {
      method: 'PUT',
      headers: {
        'Content-Length': chunk.size.toString(),
        'Content-Range': contentRange,
      },
      body: chunk,
    });
  }
  async uploadLarge(url: string, file: File, size: number, token: string) {
    const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB 的块大小，符合微软推荐的范围
    const MAX_RETRIES = 3;

    let offset = 0;
    let retries = 0;
    let lastResponse = null;

    // 确保块大小是 320KB 的倍数，这是微软推荐的做法
    if (CHUNK_SIZE % (320 * 1024) !== 0) {
      console.warn('警告：块大小应该是 320KB 的倍数以避免上传错误');
    }

    while (offset < file.size) {
      // 计算当前块的结束位置
      const end = Math.min(offset + CHUNK_SIZE, file.size);
      const chunk = file.slice(offset, end);
      const contentRange = `bytes ${offset}-${end - 1}/${file.size}`;
      console.log('Content-Range', contentRange)
      try {
        const response = await this.uploadChunk(
          url,
          chunk,
          contentRange,
          token
        );

        if (response.status === 202) { // 继续上传
          const rangeHeader = response.headers.get('Content-Range');
          if (rangeHeader) {
            offset = parseInt(rangeHeader.split('-')[1] || '0') + 1;
          } else {
            // 如果没有 Content-Range 头，尝试从响应体获取下一个范围
            const responseData = await response.json();
            if (responseData.nextExpectedRanges && responseData.nextExpectedRanges.length > 0) {
              const range = responseData.nextExpectedRanges[0].split('-');
              offset = parseInt(range[0]);
            } else {
              // 如果无法确定下一个范围，增加当前偏移量
              offset = end;
            }
          }
          retries = 0;
          lastResponse = response;
        } else if (response.ok) { // 上传完成
          return await response.json();
        } else {
          throw new Error(`上传失败，状态码: ${response.status}`);
        }
      } catch (error) {
        console.error('上传块失败:', error);
        if (retries++ >= MAX_RETRIES) throw error;
        // 使用指数退避策略
        await new Promise(res => setTimeout(res, 1000 * Math.pow(2, retries - 1)));
      }
    }

    // 如果所有块都已上传但没有收到完成响应，尝试显式提交
    if (lastResponse && offset >= file.size) {
      try {
        // 发送空请求以完成上传（适用于 deferCommit 为 true 的情况）
        const finalResponse = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Length': '0'
          }
        });

        if (finalResponse.ok) {
          return await finalResponse.json();
        }
      } catch (error) {
        console.error('提交上传失败:', error);
      }
    }

    throw new Error('上传未能完成');
  }
  async getUploadUrl(storageId: string, filePath: string, config: IConfig) {
    return fetch(`https://graph.microsoft.com/v1.0/drive/root:/${filePath}:/createUploadSession`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${await this.getToken(storageId, config)}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.json());
  }

  async uploadFile(params: IUploadFileParams): Promise<IUploadFileResult<IExtra>> {
    const config = this.readConfig(params.storageId);
    const uploadPath = this.formatUploadPath(config.filePath || DEFAULT_FILE_PATH_FORMAT, params);
    let fileResponse: FileResponse;
    if (params.fileSize > 200 * 1024 * 1024) {
      // 重写实现
      // > 200MB 大文件
      const { uploadUrl } = await this.getUploadUrl(params.storageId, uploadPath, config)
      fileResponse = await this.uploadLarge(uploadUrl, params.file, params.fileSize, config.token)
    } else {
      // 小文件
      fileResponse = await this.uploadSmall(params.storageId, params.file, uploadPath, config)
    }
    const fileShareResponse = await this.createShareLink(fileResponse.id, params.storageId, config) as FileShareResponse;
    if (!config?.serviceUrl) {
      const res = await fetch(`${DEFAULT_SERVICE_URL}/generateUrl`, {
        method: 'POST',
        body: JSON.stringify({
          token: (await utools.fetchUserServerTemporaryToken()).token,
          shareId: fileShareResponse.shareId,
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(res => res.json());
      if (res.code === 0) {
        if (fileResponse && !fileResponse.parentReference.path) {
          throw new Error(JSON.stringify(res));
        }
        return  {
          url: res.data,
          extra: {
            id: fileResponse.id,
            path: fileResponse.parentReference.path,
            driveId: fileResponse.parentReference.driveId,
            shareId: fileShareResponse.shareId,
          }
        };
      } else {
        throw new Error(res.msg);
      }
    } else {
      // 自建服务
      throw new Error('自建服务 尽情期待');
    }
  }

  async deleteFile(params: IDeleteFileParams<IExtra>): Promise<boolean> {
    const extra = params.extra;
    if (!extra) {
      throw new Error("缺少必要参数");
    }
    const config = this.readConfig(params.storageId);
    await fetch(`https://graph.microsoft.com/v1.0/me/drive/items/${extra.id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${await this.getToken(params.storageId, config)}`,
      }
    });
    return true;
  }

  async getToken(storageId: string, config: IConfig) {
    // 先尝试使用现有token
    const testResponse = await fetch('https://graph.microsoft.com/v1.0/me/drive', {
      headers: {
        Authorization: `Bearer ${ config.token }`,
      }
    });

    // 如果返回401，说明token已过期
    if (testResponse.status === 401) {
      debugger
      const res = await fetch(`${DEFAULT_SERVICE_URL}/auth/refresh-token`, {
        method: 'POST',
        body: JSON.stringify({
          refresh_token: config.refreshToken
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(res => res.json());
      console.log('res', res);
      if (res.code === 0) {
        console.log(res.data)
        const newConfig = {
          token: res.data.access_token,
          refreshToken: res.data.refresh_token,
        }
        this.saveConfig(storageId, {
          ...config,
          ...newConfig
        });
        console.log(newConfig);
        return newConfig.token;
      } else {
        throw new Error('无法刷新 token 了需要重新授权');
      }
    } else {
      return config.token;
    }
  }

  async verifyConfig(config: IConfig): Promise<void> {
  }
}
