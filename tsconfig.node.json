{
  "include": [
    "scripts/**/*"
  ],
  "compilerOptions": {
    "skipLibCheck": true,
    "noEmit": true,
    "target": "ES6",             // 目标 ECMAScript 版本
    "module": "ESNext",        // 使用 CommonJS 模块
    "strict": true,              // 启用所有严格类型检查选项
    "esModuleInterop": true,      // 允许默认导入非 ES 模块
    "moduleResolution": "Bundler",
    "types": ["node", "utools-api-types"],
  },
  "ts-node": {
    "require": ["dotenv/config"]
  }
}
